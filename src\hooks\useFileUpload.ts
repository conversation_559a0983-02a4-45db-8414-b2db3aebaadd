import { useState } from 'react';
import { uploadFile as uploadFileToStorage, FileCategory, FileMetadata } from '../services/storageService';
import { useFirebase } from '../contexts/FirebaseContext';

interface UploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  fileUrl: string | null;
  fileId: string | null;
}

interface UseFileUploadReturn {
  uploadState: UploadState;
  uploadFile: (file: File, category: FileCategory, metadata?: Partial<FileMetadata>) => Promise<void>;
  resetUploadState: () => void;
}

export const useFileUpload = (): UseFileUploadReturn => {
  const { currentUser } = useFirebase();
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    fileUrl: null,
    fileId: null
  });

  const resetUploadState = () => {
    setUploadState({
      isUploading: false,
      progress: 0,
      error: null,
      fileUrl: null,
      fileId: null
    });
  };

  const uploadFile = async (
    file: File,
    category: FileCategory,
    metadata: Partial<FileMetadata> = {}
  ) => {
    if (!currentUser) {
      setUploadState({
        ...uploadState,
        error: 'User not authenticated'
      });
      return;
    }

    try {
      setUploadState({
        ...uploadState,
        isUploading: true,
        progress: 0,
        error: null
      });

      // Create a mock progress update
      const progressInterval = setInterval(() => {
        setUploadState(prev => {
          if (prev.progress < 90) {
            return {
              ...prev,
              progress: prev.progress + 10
            };
          }
          return prev;
        });
      }, 500);

      // Upload the file
      const result = await uploadFileToStorage(
        currentUser.uid,
        file,
        category,
        metadata
      );

      clearInterval(progressInterval);

      setUploadState({
        isUploading: false,
        progress: 100,
        error: null,
        fileUrl: result.url,
        fileId: result.id
      });
    } catch (error) {
      setUploadState({
        ...uploadState,
        isUploading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred during upload'
      });
    }
  };

  return {
    uploadState,
    uploadFile,
    resetUploadState
  };
};