import React from 'react';
import { Box, Container, Typography, Link, Grid } from '@mui/material';
import Header from './Header';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <Box 
      sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: '#f5f7fa'
      }}
    >
      <Header />
      <Box component="main" sx={{ flexGrow: 1 }}>
        {children}
      </Box>
      <Box 
        component="footer" 
        sx={{ 
          bgcolor: '#1a202c', 
          color: 'white', 
          py: 6,
          mt: 'auto'
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Box sx={{ mb: { xs: 4, md: 0 } }}>
                <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', mb: 2 }}>
                  University Health
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, maxWidth: 300 }}>
                  Providing quality healthcare solutions for students and healthcare providers.
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                  Services
                </Typography>
                <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                  <Box component="li" sx={{ mb: 1 }}>
                    <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                      Telehealth
                    </Link>
                  </Box>
                  <Box component="li" sx={{ mb: 1 }}>
                    <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                      Appointments
                    </Link>
                  </Box>
                  <Box component="li" sx={{ mb: 1 }}>
                    <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                      Medical Records
                    </Link>
                  </Box>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                  Resources
                </Typography>
                <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                  <Box component="li" sx={{ mb: 1 }}>
                    <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                      Health Tips
                    </Link>
                  </Box>
                  <Box component="li" sx={{ mb: 1 }}>
                    <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                      FAQ
                    </Link>
                  </Box>
                  <Box component="li" sx={{ mb: 1 }}>
                    <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                      Support
                    </Link>
                  </Box>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                  Contact Us
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                  Email: <EMAIL>
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                  Phone: (*************
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Address: 123 University Ave, College Town
                </Typography>
              </Box>
            </Grid>
          </Grid>
          
          <Box sx={{ borderTop: '1px solid rgba(255,255,255,0.1)', mt: 4, pt: 4, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ opacity: 0.6 }}>
              © {new Date().getFullYear()} University Health Management System. All rights reserved.
            </Typography>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default Layout;






