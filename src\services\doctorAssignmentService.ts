// Doctor Assignment Service
// Manages the assignment and retrieval of doctors for students

export interface Doctor {
  id: number;
  name: string;
  specialty: string;
  department: string;
  avatar?: string;
  online?: boolean;
  assigned?: boolean;
  requested?: boolean;
}

export interface AssignedDoctor extends Doctor {
  assignedDate: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

// Mock database of available doctors
const AVAILABLE_DOCTORS: Doctor[] = [
  { id: 1, name: 'Dr. <PERSON>', specialty: 'General Practice', department: 'Primary Care', avatar: '', online: true },
  { id: 2, name: 'Dr. <PERSON>', specialty: 'Mental Health', department: 'Counseling Services', avatar: '', online: true },
  { id: 3, name: 'Dr. <PERSON>', specialty: 'Emergency Medicine', department: 'Emergency Care', avatar: '', online: false },
  { id: 4, name: 'Dr. <PERSON>', specialty: 'Internal Medicine', department: 'Internal Medicine', avatar: '', online: true },
  { id: 5, name: 'Dr. <PERSON>', specialty: 'Dermatology', department: 'Dermatology', avatar: '', online: false },
  { id: 6, name: 'Dr. <PERSON>', specialty: 'Cardiology', department: 'Cardiology', avatar: '', online: true },
  { id: 7, name: 'Dr. <PERSON> Garcia', specialty: 'Gynecology', department: 'Women\'s Health', avatar: '', online: true },
  { id: 8, name: 'Dr. Robert Brown', specialty: 'Orthopedics', department: 'Orthopedics', avatar: '', online: false },
  { id: 9, name: 'Dr. Jennifer Lee', specialty: 'Psychiatry', department: 'Mental Health', avatar: '', online: true },
  { id: 10, name: 'Dr. Thomas Anderson', specialty: 'Neurology', department: 'Neurology', avatar: '', online: false },
  { id: 11, name: 'Dr. Amanda White', specialty: 'Pediatrics', department: 'Pediatrics', avatar: '', online: true },
  { id: 12, name: 'Dr. Kevin Martinez', specialty: 'Ophthalmology', department: 'Eye Care', avatar: '', online: true },
  { id: 13, name: 'Dr. Rachel Davis', specialty: 'Endocrinology', department: 'Endocrinology', avatar: '', online: false },
  { id: 14, name: 'Dr. Steven Clark', specialty: 'Urology', department: 'Urology', avatar: '', online: true },
  { id: 15, name: 'Dr. Nicole Taylor', specialty: 'Rheumatology', department: 'Rheumatology', avatar: '', online: false }
];

// Core specialties that every student should have access to
const CORE_SPECIALTIES = ['General Practice', 'Mental Health', 'Emergency Medicine'];

/**
 * Smart doctor assignment algorithm
 * Ensures every student gets essential care coverage
 */
export const assignDoctorsToStudent = async (studentId: string): Promise<AssignedDoctor[]> => {
  console.log(`🏥 Starting doctor assignment for student: ${studentId}`);
  
  // Step 1: Assign core specialists (essential for all students)
  const coreAssignments = CORE_SPECIALTIES.map(specialty => {
    const availableDoctors = AVAILABLE_DOCTORS.filter(d => d.specialty === specialty);
    // Simple load balancing: pick randomly from available doctors
    const selectedDoctor = availableDoctors[Math.floor(Math.random() * availableDoctors.length)];
    return selectedDoctor;
  }).filter(Boolean);

  // Step 2: Assign 2 additional random specialists for comprehensive care
  const remainingDoctors = AVAILABLE_DOCTORS.filter(d => 
    !coreAssignments.some(core => core.id === d.id)
  );
  
  const additionalDoctors = remainingDoctors
    .sort(() => 0.5 - Math.random())
    .slice(0, 2);

  // Step 3: Combine assignments
  const allAssignments = [...coreAssignments, ...additionalDoctors];
  
  // Step 4: Format as AssignedDoctor objects with realistic chat data
  const mockChatData = [
    {
      lastMessage: 'Your test results came back normal. Let\'s schedule a follow-up next week.',
      lastMessageTime: '2 min ago',
      unreadCount: 2
    },
    {
      lastMessage: 'How are you feeling after starting the new medication?',
      lastMessageTime: '15 min ago',
      unreadCount: 1
    },
    {
      lastMessage: 'Remember to take your medication with food. Any side effects?',
      lastMessageTime: '1 hour ago',
      unreadCount: 0
    },
    {
      lastMessage: 'Your appointment is confirmed for tomorrow at 2 PM.',
      lastMessageTime: '3 hours ago',
      unreadCount: 3
    },
    {
      lastMessage: 'I\'ve updated your treatment plan. Please review the attached document.',
      lastMessageTime: '1 day ago',
      unreadCount: 0
    }
  ];

  const assignedDoctors: AssignedDoctor[] = allAssignments.map((doctor, index) => ({
    ...doctor,
    assignedDate: new Date().toISOString(),
    lastMessage: mockChatData[index]?.lastMessage || 'Welcome! I\'m here to help with your healthcare needs.',
    lastMessageTime: mockChatData[index]?.lastMessageTime || 'Just now',
    unreadCount: mockChatData[index]?.unreadCount || 0,
    assigned: true
  }));

  console.log(`✅ Successfully assigned ${assignedDoctors.length} doctors:`);
  assignedDoctors.forEach(doctor => {
    console.log(`   • ${doctor.name} (${doctor.specialty})`);
  });

  // Step 5: Save to localStorage (in real app, save to database)
  localStorage.setItem(`assignedDoctors_${studentId}`, JSON.stringify(assignedDoctors));
  
  return assignedDoctors;
};

/**
 * Get assigned doctors for a student
 */
export const getAssignedDoctors = (studentId: string): AssignedDoctor[] => {
  const stored = localStorage.getItem(`assignedDoctors_${studentId}`);
  if (stored) {
    return JSON.parse(stored);
  }
  return [];
};

/**
 * Get all available doctors (for browsing)
 */
export const getAllDoctors = (): Doctor[] => {
  return AVAILABLE_DOCTORS.map(doctor => ({
    ...doctor,
    assigned: false,
    requested: false
  }));
};

/**
 * Request assignment of a new doctor
 */
export const requestDoctorAssignment = async (studentId: string, doctorId: number): Promise<boolean> => {
  console.log(`📋 Processing doctor assignment request: Student ${studentId} → Doctor ${doctorId}`);
  
  // In a real app, this would create a request in the database
  // For now, we'll simulate the request process
  
  const doctor = AVAILABLE_DOCTORS.find(d => d.id === doctorId);
  if (!doctor) {
    console.error('❌ Doctor not found');
    return false;
  }

  // Simulate approval process (in real app, this would be handled by admin/doctor)
  setTimeout(() => {
    console.log(`✅ Doctor assignment request approved: ${doctor.name}`);
    
    // Add to assigned doctors
    const currentAssigned = getAssignedDoctors(studentId);
    const newAssignment: AssignedDoctor = {
      ...doctor,
      assignedDate: new Date().toISOString(),
      lastMessage: `Hello! I'm ${doctor.name}. I'm now available to help you.`,
      lastMessageTime: 'Just now',
      unreadCount: 1,
      assigned: true
    };
    
    const updatedAssigned = [...currentAssigned, newAssignment];
    localStorage.setItem(`assignedDoctors_${studentId}`, JSON.stringify(updatedAssigned));
    
  }, 2000); // 2 second delay to simulate processing
  
  return true;
};

/**
 * Get assignment statistics for admin dashboard
 */
export const getAssignmentStats = () => {
  const totalDoctors = AVAILABLE_DOCTORS.length;
  const totalStudents = 150; // Mock number
  const avgAssignmentsPerStudent = 5;
  
  return {
    totalDoctors,
    totalStudents,
    avgAssignmentsPerStudent,
    totalAssignments: totalStudents * avgAssignmentsPerStudent,
    coreSpecialtiesCoverage: '100%', // All students get core specialties
    mostRequestedSpecialty: 'Mental Health',
    leastRequestedSpecialty: 'Rheumatology'
  };
};
