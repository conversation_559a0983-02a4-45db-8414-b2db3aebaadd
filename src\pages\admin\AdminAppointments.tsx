import React, { useState } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';

// Icons
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import MoreVertIcon from '@mui/icons-material/MoreVert';

const AdminAppointments = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDate, setFilterDate] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  
  // Mock data for appointments
  const appointments = [
    { id: 1, student: 'John Smith', doctor: 'Dr. Sarah Johnson', date: '2023-05-15', time: '10:00 AM', status: 'scheduled', reason: 'Regular checkup' },
    { id: 2, student: 'Emma Davis', doctor: 'Dr. Michael Chen', date: '2023-05-15', time: '11:30 AM', status: 'completed', reason: 'Flu symptoms' },
    { id: 3, student: 'Alex Rodriguez', doctor: 'Dr. Sarah Johnson', date: '2023-05-16', time: '09:15 AM', status: 'cancelled', reason: 'Mental health consultation' },
    { id: 4, student: 'Lisa Wang', doctor: 'Dr. Michael Chen', date: '2023-05-16', time: '02:00 PM', status: 'scheduled', reason: 'Vaccination' },
    { id: 5, student: 'William Taylor', doctor: 'Dr. Emily White', date: '2023-05-17', time: '10:45 AM', status: 'scheduled', reason: 'Allergy symptoms' },
    { id: 6, student: 'Olivia Brown', doctor: 'Dr. James Wilson', date: '2023-05-17', time: '03:30 PM', status: 'scheduled', reason: 'Sports injury' },
    { id: 7, student: 'Daniel Lee', doctor: 'Dr. Emily White', date: '2023-05-18', time: '11:00 AM', status: 'scheduled', reason: 'Headache and dizziness' },
    { id: 8, student: 'Sophia Martinez', doctor: 'Dr. James Wilson', date: '2023-05-18', time: '01:15 PM', status: 'scheduled', reason: 'Annual physical' },
    { id: 9, student: 'John Smith', doctor: 'Dr. Michael Chen', date: '2023-05-10', time: '09:30 AM', status: 'completed', reason: 'Follow-up visit' },
    { id: 10, student: 'Emma Davis', doctor: 'Dr. Sarah Johnson', date: '2023-05-11', time: '02:45 PM', status: 'completed', reason: 'Prescription refill' },
    { id: 11, student: 'Alex Rodriguez', doctor: 'Dr. Emily White', date: '2023-05-12', time: '10:15 AM', status: 'cancelled', reason: 'Skin rash examination' },
    { id: 12, student: 'Lisa Wang', doctor: 'Dr. James Wilson', date: '2023-05-12', time: '03:00 PM', status: 'completed', reason: 'Eye examination' }
  ];

  // Filter appointments based on search term and filters
  const filteredAppointments = appointments.filter(appointment => {
    const matchesSearch = appointment.student.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          appointment.doctor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          appointment.reason.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || appointment.status === filterStatus;
    const matchesDate = !filterDate || appointment.date === filterDate;
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  // Pagination handlers
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Menu handlers
  const handleOpenMenu = (event, appointment) => {
    setAnchorEl(event.currentTarget);
    setSelectedAppointment(appointment);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  // Delete dialog handlers
  const handleOpenDeleteDialog = () => {
    setOpenDeleteDialog(true);
    handleCloseMenu();
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedAppointment(null);
  };

  const handleDeleteAppointment = () => {
    // Here you would implement the actual deletion logic
    console.log(`Deleting appointment with ID: ${selectedAppointment.id}`);
    handleCloseDeleteDialog();
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Appointment Management
          </Typography>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/admin/appointments/new')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            Schedule Appointment
          </Button>
        </Box>

        {/* Filters and Search */}
        <Paper 
          sx={{ 
            p: 3, 
            mb: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder="Search appointments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ flexGrow: 1, minWidth: '200px' }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              size="small"
            />
            
            <FormControl sx={{ minWidth: '150px' }} size="small">
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                id="status-filter"
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              label="Date"
              type="date"
              value={filterDate}
              onChange={(e) => setFilterDate(e.target.value)}
              InputLabelProps={{
                shrink: true,
              }}
              size="small"
              sx={{ minWidth: '150px' }}
            />
          </Box>
        </Paper>

        {/* Appointments Table */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden'
          }}
        >
          <TableContainer>
            <Table sx={{ minWidth: 650 }} aria-label="appointments table">
              <TableHead>
                <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                  <TableCell>Student</TableCell>
                  <TableCell>Doctor</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Time</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredAppointments
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((appointment) => (
                    <TableRow
                      key={appointment.id}
                      sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                    >
                      <TableCell>{appointment.student}</TableCell>
                      <TableCell>{appointment.doctor}</TableCell>
                      <TableCell>{appointment.date}</TableCell>
                      <TableCell>{appointment.time}</TableCell>
                      <TableCell>{appointment.reason}</TableCell>
                      <TableCell>
                        <Chip 
                          label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)} 
                          color={
                            appointment.status === 'scheduled' ? 'primary' : 
                            appointment.status === 'completed' ? 'success' : 'error'
                          }
                          size="small"
                          variant={appointment.status === 'cancelled' ? 'outlined' : 'filled'}
                        />
                      </TableCell>
                      <TableCell align="right">
                        <IconButton 
                          size="small" 
                          onClick={() => navigate(`/admin/appointments/${appointment.id}`)}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          onClick={() => navigate(`/admin/appointments/${appointment.id}/edit`)}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="error"
                          onClick={(e) => handleOpenMenu(e, appointment)}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                {filteredAppointments.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        No appointments found matching your criteria
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredAppointments.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </Container>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        PaperProps={{
          elevation: 3,
          sx: { borderRadius: 2, minWidth: 150 }
        }}
      >
        <MenuItem onClick={() => {
          navigate(`/admin/appointments/${selectedAppointment?.id}`);
          handleCloseMenu();
        }}>
          <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => {
          navigate(`/admin/appointments/${selectedAppointment?.id}/edit`);
          handleCloseMenu();
        }}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit Appointment
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleOpenDeleteDialog} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Cancel Appointment
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: { borderRadius: 3 }
        }}
      >
        <DialogTitle id="alert-dialog-title">
          {"Confirm Appointment Cancellation"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to cancel the appointment for {selectedAppointment?.student} with {selectedAppointment?.doctor} on {selectedAppointment?.date} at {selectedAppointment?.time}? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button 
            onClick={handleCloseDeleteDialog} 
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            No, Keep It
          </Button>
          <Button 
            onClick={handleDeleteAppointment} 
            color="error" 
            variant="contained"
            sx={{ borderRadius: 2 }}
            autoFocus
          >
            Yes, Cancel It
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default AdminAppointments;
