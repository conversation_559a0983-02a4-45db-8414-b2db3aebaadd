import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  updateEmail,
  updatePassword,
  reauthenticateWithCredential,
  EmailAuthProvider,
  type User
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, serverTimestamp, collection, getDocs, query, where } from 'firebase/firestore';
import { auth, db } from './firebase';

// User roles
export type UserRole = 'student' | 'doctor' | 'admin';

// User profile data
export interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  role: UserRole;
  phoneNumber?: string;
  department?: string;
  specialty?: string; // For doctors
  studentId?: string; // For students
  createdAt: Date;
  updatedAt: Date;
}

// Define a type for Firebase User
export type FirebaseUser = User;

/**
 * Register a new user
 */
export const registerUser = async (
  email: string,
  password: string,
  role: UserRole = 'student',
  profileData: Partial<UserProfile> = {}
): Promise<UserProfile> => {
  try {
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Set display name if provided or use email username
    const displayName = profileData.displayName || email.split('@')[0];
    await updateProfile(user, { displayName, photoURL: profileData.photoURL });
    
    // Create user document in Firestore
    const userData: UserProfile = {
      uid: user.uid,
      email: user.email || email,
      displayName,
      photoURL: profileData.photoURL,
      role,
      ...profileData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await setDoc(doc(db, 'users', user.uid), {
      ...userData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return userData;
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

/**
 * Login a user
 */
export const loginUser = async (email: string, password: string): Promise<FirebaseUser> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

/**
 * Logout the current user
 */
export const logoutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error logging out:', error);
    throw error;
  }
};

/**
 * Get user profile from Firestore
 */
export const getUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    
    if (userDoc.exists()) {
      return userDoc.data() as UserProfile;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

/**
 * Update user profile
 */
export const updateUserProfile = async (
  userId: string,
  profileData: Partial<UserProfile>
): Promise<void> => {
  try {
    // Update in Firestore
    await updateDoc(doc(db, 'users', userId), {
      ...profileData,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

/**
 * Change user role
 * Only admins should be able to call this function
 */
export const changeUserRole = async (
  userId: string,
  newRole: UserRole
): Promise<void> => {
  try {
    // First update the role in Firestore
    await updateDoc(doc(db, 'users', userId), {
      role: newRole,
      updatedAt: serverTimestamp()
    });
    
    // If changing to student role, assign doctors if they don't have any
    if (newRole === 'student') {
      // Check if student already has doctor assignments
      const assignmentDoc = await getDoc(doc(db, 'doctorAssignments', userId));
      
      if (!assignmentDoc.exists()) {
        // Assign doctors to the student
        const doctorsQuery = query(
          collection(db, 'users'),
          where('role', '==', 'doctor')
        );
        
        const doctorSnapshot = await getDocs(doctorsQuery);
        const availableDoctors = doctorSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        // Create mock doctors if needed
        const mockDoctors = [
          {
            id: 'mock-doc-1',
            name: 'Dr. John Smith',
            specialty: 'General Practice',
            department: 'Primary Care'
          },
          {
            id: 'mock-doc-2',
            name: 'Dr. Sarah Johnson',
            specialty: 'Mental Health',
            department: 'Psychiatry'
          },
          {
            id: 'mock-doc-3',
            name: 'Dr. Michael Chen',
            specialty: 'Emergency Medicine',
            department: 'Emergency'
          }
        ];
        
        // Combine real and mock doctors if needed
        const allDoctors = availableDoctors.length >= 3 
          ? availableDoctors 
          : [...availableDoctors, ...mockDoctors.slice(0, 5 - availableDoctors.length)];
        
        // Assign at least 3 doctors to the student
        const assignedDoctors = allDoctors.slice(0, 5);
        
        // Save the assignments to Firestore
        await setDoc(doc(db, 'doctorAssignments', userId), {
          doctors: assignedDoctors.map(doctor => ({
            id: doctor.id,
            name: doctor.name || doctor.displayName,
            specialty: doctor.specialty || 'General Practice',
            department: doctor.department || 'Primary Care',
            assignedDate: new Date().toISOString()
          })),
          updatedAt: new Date().toISOString()
        });
      }
    }
    
    console.log(`User role changed to ${newRole} for user ${userId}`);
  } catch (error) {
    console.error('Error changing user role:', error);
    throw error;
  }
};

/**
 * Create a new user (admin function)
 * This allows admins to create accounts for doctors or other admins
 */
export const createUserByAdmin = async (
  email: string,
  password: string,
  role: UserRole,
  profileData: Partial<UserProfile> = {}
): Promise<UserProfile> => {
  // This should only be called by admins
  return registerUser(email, password, role, profileData);
};

/**
 * Get all users (admin function)
 */
export const getAllUsers = async (): Promise<UserProfile[]> => {
  try {
    const usersSnapshot = await getDocs(collection(db, 'users'));
    return usersSnapshot.docs.map(doc => doc.data() as UserProfile);
  } catch (error) {
    console.error('Error getting all users:', error);
    throw error;
  }
};

/**
 * Send password reset email
 */
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
};

/**
 * Change user password
 */
export const changePassword = async (
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  try {
    const user = auth.currentUser;
    
    if (!user || !user.email) {
      throw new Error('No authenticated user found');
    }
    
    // Re-authenticate user before changing password
    const credential = EmailAuthProvider.credential(user.email, currentPassword);
    await reauthenticateWithCredential(user, credential);
    
    // Change password
    await updatePassword(user, newPassword);
  } catch (error) {
    console.error('Error changing password:', error);
    throw error;
  }
};



