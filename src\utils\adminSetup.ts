import { doc, setDoc, getDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { auth, db } from '../services/firebase';
import type { UserProfile, UserRoles } from '../types/firebase';

/**
 * Create an admin user directly
 */
const createAdminUser = async (email: string, password: string, displayName: string): Promise<void> => {
  try {
    // Create the user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create admin profile in Firestore
    const adminProfile: UserProfile = {
      uid: user.uid,
      email: email,
      displayName: displayName,
      role: UserRoles.ADMIN,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('🔧 Creating admin with role:', UserRoles.ADMIN, 'type:', typeof UserRoles.ADMIN);
    await setDoc(doc(db, 'users', user.uid), adminProfile);
    console.log('✅ Admin user created successfully');

    // Sign out after creation (so they can sign in normally)
    await signOut(auth);
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
};

/**
 * Setup script to create admin users
 * Call this function in the browser console to create admin accounts
 */
export const setupAdminUsers = async () => {
  try {
    console.log('🚀 Setting up admin users...');

    // Create admin users
    const adminUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        displayName: 'System Administrator'
      },
      {
        email: '<EMAIL>',
        password: 'admin123',
        displayName: 'Test Admin'
      },
      {
        email: '<EMAIL>',
        password: 'admin123',
        displayName: 'Admin User'
      }
    ];

    for (const admin of adminUsers) {
      try {
        await createAdminUser(admin.email, admin.password, admin.displayName);
        console.log(`✅ Created admin user: ${admin.email}`);
      } catch (error: any) {
        if (error.code === 'auth/email-already-in-use') {
          console.log(`ℹ️ Admin user already exists: ${admin.email}`);
        } else {
          console.error(`❌ Error creating admin user ${admin.email}:`, error);
        }
      }
    }

    console.log('✅ Admin setup complete!');
    console.log('📋 Admin credentials:');
    adminUsers.forEach(admin => {
      console.log(`   Email: ${admin.email} | Password: ${admin.password}`);
    });

  } catch (error) {
    console.error('❌ Error setting up admin users:', error);
  }
};

/**
 * Update an existing user's role to admin
 */
export const makeUserAdmin = async (userId: string, email: string, displayName: string) => {
  try {
    const adminProfile: UserProfile = {
      uid: userId,
      email: email,
      displayName: displayName,
      role: UserRoles.ADMIN,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await setDoc(doc(db, 'users', userId), adminProfile);
    console.log(`✅ User ${email} is now an admin`);
  } catch (error) {
    console.error('Error updating user role:', error);
  }
};

/**
 * Debug function to check current user's role
 */
export const checkCurrentUserRole = async () => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log('❌ No user is currently logged in');
      return;
    }

    console.log('🔍 Current user:', currentUser.email);

    // Get user profile from Firestore
    const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
    if (userDoc.exists()) {
      const profile = userDoc.data();
      console.log('👤 User profile:', {
        email: profile.email,
        role: profile.role,
        roleType: typeof profile.role,
        roleValue: JSON.stringify(profile.role)
      });
      console.log('🔧 UserRoles.ADMIN:', UserRoles.ADMIN, 'type:', typeof UserRoles.ADMIN);
      console.log('✅ Role match:', profile.role === UserRoles.ADMIN);
    } else {
      console.log('❌ No user profile found in Firestore');
    }
  } catch (error) {
    console.error('❌ Error checking user role:', error);
  }
};

// Make functions available globally for console access
if (typeof window !== 'undefined') {
  (window as any).setupAdminUsers = setupAdminUsers;
  (window as any).makeUserAdmin = makeUserAdmin;
  (window as any).checkCurrentUserRole = checkCurrentUserRole;
}
