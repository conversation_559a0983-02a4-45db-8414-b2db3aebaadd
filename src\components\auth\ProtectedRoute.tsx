import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { CircularProgress, Box, Typography } from '@mui/material';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles = []
}) => {
  const { currentUser, userProfile, loading } = useAuth();
  const location = useLocation();
  const [profileLoading, setProfileLoading] = React.useState(true);

  // Additional check to ensure profile is fully loaded
  React.useEffect(() => {
    if (!loading && currentUser) {
      // Give a small delay to ensure profile is loaded
      const timer = setTimeout(() => {
        setProfileLoading(false);
      }, 500);

      return () => clearTimeout(timer);
    } else if (!loading && !currentUser) {
      setProfileLoading(false);
    }
  }, [loading, currentUser, userProfile]);

  // Show loading spinner while auth state is being determined
  if (loading || (currentUser && profileLoading)) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          gap: 2
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="body2" color="text.secondary">
          Loading your profile...
        </Typography>
      </Box>
    );
  }

  // If not logged in, redirect to login
  if (!currentUser) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If user is logged in but profile is still not loaded, show loading
  if (!userProfile) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          gap: 2
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="body2" color="text.secondary">
          Setting up your profile...
        </Typography>
      </Box>
    );
  }

  // Debug logging
  console.log('ProtectedRoute check:', {
    userEmail: userProfile.email,
    userRole: userProfile.role,
    userRoleType: typeof userProfile.role,
    allowedRoles,
    allowedRolesTypes: allowedRoles.map(role => typeof role),
    hasAccess: allowedRoles.length === 0 || allowedRoles.includes(userProfile.role),
    includesCheck: allowedRoles.includes(userProfile.role),
    exactMatch: allowedRoles.find(role => role === userProfile.role)
  });

  // If roles are specified and user doesn't have the required role
  if (allowedRoles.length > 0 && !allowedRoles.includes(userProfile.role)) {
    console.log('Access denied:', {
      userRole: userProfile.role,
      allowedRoles,
      path: location.pathname
    });
    return <Navigate to="/unauthorized" replace />;
  }

  // If all checks pass, render the protected component
  return <>{children}</>;
};

export default ProtectedRoute;

