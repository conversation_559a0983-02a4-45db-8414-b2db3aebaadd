import React, { useState } from 'react';
import {
  Container,
  Box,
  Typo<PERSON>,
  TextField,
  Button,
  Paper,
  Link,
  CircularProgress,
  Alert,
  InputAdornment,
  IconButton,
  Grid
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  HealthAndSafety as HealthAndSafetyIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { loginUser } from '../../services/authService';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/layout/Layout';
import '../../utils/adminSetup'; // Import admin setup functions

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, userProfile, loading: authLoading } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Redirect if already logged in and profile is loaded
  React.useEffect(() => {
    if (currentUser && userProfile && !authLoading) {
      console.log('🔄 Redirecting user:', {
        email: userProfile.email,
        role: userProfile.role
      });
      redirectBasedOnRole();
    }
  }, [currentUser, userProfile, authLoading]);

  const redirectBasedOnRole = () => {
    if (!userProfile) {
      console.log('⚠️ No user profile available for redirect');
      return;
    }

    console.log(`🎯 Redirecting ${userProfile.role} to appropriate dashboard`);

    // Redirect based on user role
    switch (userProfile.role) {
      case 'admin':
        console.log('👑 Redirecting admin to admin dashboard');
        navigate('/admin/dashboard', { replace: true });
        break;
      case 'doctor':
        console.log('👨‍⚕️ Redirecting doctor to doctor dashboard');
        navigate('/doctor/dashboard', { replace: true });
        break;
      case 'student':
        console.log('🎓 Redirecting student to student dashboard');
        navigate('/dashboard', { replace: true });
        break;
      default:
        console.log('🔄 Unknown role, redirecting to default dashboard');
        navigate('/dashboard', { replace: true });
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    try {
      setError('');
      setLoading(true);

      console.log('🔐 Attempting login for:', email);
      await loginUser(email, password);
      console.log('✅ Login successful, waiting for profile...');

      // The useEffect will handle the redirect based on user role
      // Don't set loading to false here - let the redirect happen

    } catch (err: any) {
      console.error('❌ Login error:', err);
      setError(err.message || 'Failed to log in. Please check your credentials.');
      setLoading(false);
    }
  };
  
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Layout>
      <Container maxWidth="sm">
        <Box sx={{
          mt: { xs: 4, md: 8 },
          mb: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 3, md: 5 },
              borderRadius: 3,
              width: '100%',
              boxShadow: '0 8px 40px rgba(0,0,0,0.12)',
              background: 'linear-gradient(to bottom, #ffffff, #f9fafc)'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
              <HealthAndSafetyIcon
                sx={{
                  fontSize: 40,
                  color: 'primary.main',
                  mr: 1.5
                }}
              />
              <Typography
                variant="h4"
                component="h1"
                fontWeight="bold"
                color="primary.main"
              >
                Sign In
              </Typography>
            </Box>

            <Typography
              variant="body1"
              color="text.secondary"
              align="center"
              sx={{ mb: 3 }}
            >
              Welcome back to our healthcare platform
            </Typography>

            {/* Development Helper */}
            {process.env.NODE_ENV === 'development' && (
              <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Development Mode:</strong>
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  To create admin users, open browser console and run: <code>setupAdminUsers()</code>
                </Typography>
                <Typography variant="body2">
                  Default admin: <EMAIL> / admin123
                </Typography>
              </Alert>
            )}
          
            {error && (
              <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit} noValidate>
              <TextField
                margin="normal"
                required
                fullWidth
                id="email"
                label="Email Address"
                name="email"
                autoComplete="email"
                autoFocus
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="Password"
                type={showPassword ? 'text' : 'password'}
                id="password"
                autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Box sx={{ textAlign: 'right', mt: 1, mb: 3 }}>
                <Link
                  component={RouterLink}
                  to="/forgot-password"
                  variant="body2"
                  sx={{
                    fontWeight: 'bold',
                    color: 'primary.main',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  }}
                >
                  Forgot password?
                </Link>
              </Box>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                color="primary"
                size="large"
                disabled={loading}
                sx={{
                  mt: 1,
                  mb: 3,
                  py: 1.5,
                  borderRadius: 2,
                  fontWeight: 'bold',
                  boxShadow: '0 4px 12px rgba(0,114,255,0.3)'
                }}
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  'Sign In'
                )}
              </Button>

              <Grid container justifyContent="center">
                <Grid item>
                  <Typography variant="body2" color="text.secondary">
                    Don't have an account?{' '}
                    <Link
                      component={RouterLink}
                      to="/register"
                      sx={{
                        fontWeight: 'bold',
                        color: 'primary.main',
                        textDecoration: 'none',
                        '&:hover': {
                          textDecoration: 'underline'
                        }
                      }}
                    >
                      Sign Up
                    </Link>
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default Login;


