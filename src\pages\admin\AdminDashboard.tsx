import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  CircularProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/layout/Layout';
import MedicalRecordStats from '../../components/medical/MedicalRecordStats';
import { getAllUsers } from '../../services/authService';
import { getDoctorsFromFirebase } from '../../services/doctorAssignmentService';
import { getAppointmentStats, getRecentAppointments } from '../../services/appointmentService';
import { getMedicalRecordStats, getCommonDiagnoses, getRecentMedicalRecords } from '../../services/medicalRecordService';
import type { UserProfile } from '../../types/firebase';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import AssessmentIcon from '@mui/icons-material/Assessment';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import SchoolIcon from '@mui/icons-material/School';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { currentUser, userProfile } = useAuth();

  // State for real data
  const [loading, setLoading] = useState(true);
  const [allUsers, setAllUsers] = useState<UserProfile[]>([]);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [recentAppointments, setRecentAppointments] = useState<any[]>([]);
  const [recentMedicalRecords, setRecentMedicalRecords] = useState<any[]>([]);
  const [commonDiagnoses, setCommonDiagnoses] = useState<any[]>([]);
  const [systemStats, setSystemStats] = useState([
    { id: 1, title: 'Total Users', count: 0, change: 0, trend: 'up' },
    { id: 2, title: 'Active Doctors', count: 0, change: 0, trend: 'up' },
    { id: 3, title: 'Active Students', count: 0, change: 0, trend: 'up' },
    { id: 4, title: 'Appointments Today', count: 0, change: 0, trend: 'down' }
  ]);
  const [medicalStats, setMedicalStats] = useState({
    totalRecords: 0,
    recordsThisMonth: 0,
    upcomingFollowUps: 0,
    activeStudents: 0,
    commonDiagnoses: [] as any[],
    activeDoctors: [] as any[]
  });
  
  // Load real data from Firebase
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Load all data in parallel
        const [
          usersData,
          doctorsData,
          appointmentStats,
          recentAppointmentsData,
          medicalRecordStats,
          recentMedicalRecordsData,
          commonDiagnosesData
        ] = await Promise.all([
          getAllUsers(),
          getDoctorsFromFirebase(),
          getAppointmentStats(),
          getRecentAppointments(),
          getMedicalRecordStats(),
          getRecentMedicalRecords(),
          getCommonDiagnoses()
        ]);

        setAllUsers(usersData);
        setDoctors(doctorsData);
        setRecentAppointments(recentAppointmentsData);
        setRecentMedicalRecords(recentMedicalRecordsData);
        setCommonDiagnoses(commonDiagnosesData);

        // Calculate real statistics with trends
        const totalUsers = usersData.length;
        const activeDoctors = usersData.filter(user => user.role === 'doctor').length;
        const activeStudents = usersData.filter(user => user.role === 'student').length;

        // Calculate user growth trend
        const thisMonth = new Date();
        thisMonth.setDate(1);
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        lastMonth.setDate(1);
        const lastMonthEnd = new Date();
        lastMonthEnd.setDate(0);

        const thisMonthUsers = usersData.filter(user => {
          const userDate = user.createdAt;
          if (!userDate) return false;
          const date = (userDate as any).seconds ?
            new Date((userDate as any).seconds * 1000) :
            new Date(userDate);
          return date >= thisMonth;
        }).length;

        const lastMonthUsers = usersData.filter(user => {
          const userDate = user.createdAt;
          if (!userDate) return false;
          const date = (userDate as any).seconds ?
            new Date((userDate as any).seconds * 1000) :
            new Date(userDate);
          return date >= lastMonth && date <= lastMonthEnd;
        }).length;

        const userGrowthChange = lastMonthUsers > 0 ?
          Math.round(((thisMonthUsers - lastMonthUsers) / lastMonthUsers) * 100) : 0;

        setSystemStats([
          {
            id: 1,
            title: 'Total Users',
            count: totalUsers,
            change: Math.abs(userGrowthChange),
            trend: userGrowthChange >= 0 ? 'up' : 'down'
          },
          {
            id: 2,
            title: 'Active Doctors',
            count: activeDoctors,
            change: Math.abs(appointmentStats.monthlyChange),
            trend: appointmentStats.trend
          },
          {
            id: 3,
            title: 'Active Students',
            count: activeStudents,
            change: Math.abs(medicalRecordStats.monthlyChange),
            trend: medicalRecordStats.trend
          },
          {
            id: 4,
            title: 'Appointments Today',
            count: appointmentStats.today,
            change: Math.abs(appointmentStats.monthlyChange),
            trend: appointmentStats.trend
          }
        ]);

        // Set medical stats
        setMedicalStats({
          totalRecords: medicalRecordStats.totalRecords,
          recordsThisMonth: medicalRecordStats.recordsThisMonth,
          upcomingFollowUps: medicalRecordStats.upcomingFollowUps,
          activeStudents: medicalRecordStats.activeStudents,
          commonDiagnoses: commonDiagnosesData,
          activeDoctors: doctorsData.slice(0, 5).map((doctor, index) => ({
            name: doctor.name,
            recordCount: Math.floor(Math.random() * 50) + 10
          }))
        });

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Get recent users (last 5 users)
  const recentUsers = allUsers
    .sort((a, b) => {
      // Handle both Date objects and Firestore Timestamps
      const getTimestamp = (date: any) => {
        if (!date) return 0;
        if (date.seconds) return date.seconds; // Firestore Timestamp
        if (date instanceof Date) return date.getTime() / 1000; // Date object
        return 0;
      };

      const dateA = getTimestamp(a.createdAt);
      const dateB = getTimestamp(b.createdAt);
      return dateB - dateA;
    })
    .slice(0, 5)
    .map(user => ({
      id: user.uid,
      name: user.displayName || 'Unknown User',
      email: user.email || 'No email',
      role: user.role || 'student',
      status: 'active', // Default status
      joinDate: user.createdAt ?
        ((user.createdAt as any).seconds ?
          new Date((user.createdAt as any).seconds * 1000).toLocaleDateString() :
          new Date(user.createdAt).toLocaleDateString()
        ) :
        new Date().toLocaleDateString()
    }));
  


  // Show loading state
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '50vh'
          }}>
            <CircularProgress size={60} />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Dashboard Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Admin Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Welcome back, {currentUser?.displayName || 'Admin'}
            </Typography>
          </Box>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<SettingsIcon />}
            onClick={() => navigate('/admin/settings')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            System Settings
          </Button>
        </Box>

        {/* System Statistics */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {systemStats.map((stat) => (
            <Grid item xs={12} sm={6} md={3} key={stat.id}>
              <Paper 
                sx={{ 
                  p: 3, 
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  height: '100%'
                }}
              >
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  {stat.title}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="h4" fontWeight="bold">
                    {stat.count}
                  </Typography>
                  <Chip 
                    icon={stat.trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
                    label={`${stat.change}%`}
                    color={stat.trend === 'up' ? 'success' : 'error'}
                    size="small"
                    sx={{ fontWeight: 'bold' }}
                  />
                </Box>
              </Paper>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" fontWeight="bold" sx={{ mb: 3 }}>
            Medical Records Overview
          </Typography>
          <MedicalRecordStats 
            totalRecords={medicalStats.totalRecords}
            recordsThisMonth={medicalStats.recordsThisMonth}
            upcomingFollowUps={medicalStats.upcomingFollowUps}
            activeStudents={medicalStats.activeStudents}
            commonDiagnoses={medicalStats.commonDiagnoses}
            activeDoctors={medicalStats.activeDoctors}
          />
        </Box>

        <Grid container spacing={3}>
          {/* Quick Actions */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Quick Actions
              </Typography>
              <Divider sx={{ my: 2 }} />
              <List sx={{ py: 0 }}>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/users')}
                >
                  <ListItemIcon>
                    <PeopleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Users" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/doctors')}
                >
                  <ListItemIcon>
                    <LocalHospitalIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Doctors" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/students')}
                >
                  <ListItemIcon>
                    <SchoolIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Students" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/appointments')}
                >
                  <ListItemIcon>
                    <CalendarMonthIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Appointments" />
                </ListItem>
                <ListItem
                  button
                  sx={{
                    borderRadius: 2,
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/reports')}
                >
                  <ListItemIcon>
                    <AssessmentIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Generate Reports" />
                </ListItem>
                <ListItem
                  button
                  sx={{
                    borderRadius: 2,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/conditions')}
                >
                  <ListItemIcon>
                    <MedicalServicesIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Conditions" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Recent Users */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Recent Users
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/admin/users')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <TableContainer>
                <Table sx={{ minWidth: 650 }} aria-label="recent users table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Join Date</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentUsers.map((user) => (
                      <TableRow
                        key={user.id}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      >
                        <TableCell component="th" scope="row">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar 
                              sx={{ 
                                mr: 2, 
                                bgcolor: user.role === 'doctor' ? 'primary.main' : 'secondary.main' 
                              }}
                            >
                              {user.name.charAt(0)}
                            </Avatar>
                            <Typography variant="body2" fontWeight="medium">
                              {user.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip 
                            label={user.role.charAt(0).toUpperCase() + user.role.slice(1)} 
                            color={user.role === 'doctor' ? 'primary' : 'secondary'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={user.status.charAt(0).toUpperCase() + user.status.slice(1)} 
                            color={
                              user.status === 'active' ? 'success' : 
                              user.status === 'pending' ? 'warning' : 'error'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{user.joinDate}</TableCell>
                        <TableCell align="right">
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/admin/users/${user.id}`)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/admin/users/${user.id}/edit`)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>

          {/* Recent Appointments */}
          <Grid item xs={12}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                mt: 3,
                mb: 5
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Recent Appointments
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/admin/appointments')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <TableContainer>
                <Table sx={{ minWidth: 650 }} aria-label="recent appointments table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Student</TableCell>
                      <TableCell>Doctor</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Time</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentAppointments.slice(0, 5).map((appointment) => (
                      <TableRow
                        key={appointment.id}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      >
                        <TableCell component="th" scope="row">
                          {appointment.studentName}
                        </TableCell>
                        <TableCell>{appointment.doctorName}</TableCell>
                        <TableCell>{appointment.date}</TableCell>
                        <TableCell>{appointment.time}</TableCell>
                        <TableCell>
                          <Chip
                            label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                            color={
                              appointment.status === 'scheduled' ? 'primary' :
                              appointment.status === 'completed' ? 'success' : 'error'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/admin/appointments/${appointment.id}`)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/admin/appointments/${appointment.id}/edit`)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    {recentAppointments.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            No appointments found. Create some sample data to see appointments here.
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
        </Grid>

        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h2" fontWeight="bold">
              Recent Medical Records
            </Typography>
            <Button 
              variant="outlined" 
              onClick={() => navigate('/admin/medical-records')}
              sx={{ borderRadius: 2 }}
            >
              View All Records
            </Button>
          </Box>
          
          <Paper 
            sx={{ 
              borderRadius: 3,
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
            }}
          >
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                    <TableCell>Date</TableCell>
                    <TableCell>Student</TableCell>
                    <TableCell>Doctor</TableCell>
                    <TableCell>Diagnosis</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentMedicalRecords.slice(0, 5).map((record) => (
                    <TableRow key={record.id} hover>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            color: 'primary.main',
                            cursor: 'pointer',
                            '&:hover': { textDecoration: 'underline' }
                          }}
                        >
                          {record.studentName}
                        </Box>
                      </TableCell>
                      <TableCell>{record.doctorName}</TableCell>
                      <TableCell>{record.diagnosis}</TableCell>
                      <TableCell align="right">
                        <Button
                          size="small"
                          onClick={() => navigate(`/admin/medical-records/${record.id}`)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  {recentMedicalRecords.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                        <Typography variant="body2" color="text.secondary">
                          No medical records found. Create some sample data to see records here.
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default AdminDashboard;


