import React from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Paper, 
  Button,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useFirebase } from '../../contexts/FirebaseContext';
import Layout from '../../components/layout/Layout';
import MedicalRecordStats from '../../components/medical/MedicalRecordStats';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import AssessmentIcon from '@mui/icons-material/Assessment';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import SchoolIcon from '@mui/icons-material/School';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  
  // Mock data for system statistics
  const systemStats = [
    { id: 1, title: 'Total Users', count: 1254, change: 12, trend: 'up' },
    { id: 2, title: 'Active Doctors', count: 48, change: 5, trend: 'up' },
    { id: 3, title: 'Active Students', count: 1206, change: 8, trend: 'up' },
    { id: 4, title: 'Appointments Today', count: 87, change: 3, trend: 'down' }
  ];
  
  // Mock data for recent users
  const recentUsers = [
    { id: 1, name: 'John Smith', email: '<EMAIL>', role: 'student', status: 'active', joinDate: '2023-05-10' },
    { id: 2, name: 'Dr. Sarah Johnson', email: '<EMAIL>', role: 'doctor', status: 'active', joinDate: '2023-05-09' },
    { id: 3, name: 'Emma Davis', email: '<EMAIL>', role: 'student', status: 'pending', joinDate: '2023-05-08' },
    { id: 4, name: 'Dr. Michael Chen', email: '<EMAIL>', role: 'doctor', status: 'active', joinDate: '2023-05-07' },
    { id: 5, name: 'Alex Rodriguez', email: '<EMAIL>', role: 'student', status: 'inactive', joinDate: '2023-05-06' }
  ];
  
  // Mock data for recent appointments
  const recentAppointments = [
    { id: 1, student: 'John Smith', doctor: 'Dr. Sarah Johnson', date: '2023-05-15', time: '10:00 AM', status: 'scheduled' },
    { id: 2, student: 'Emma Davis', doctor: 'Dr. Michael Chen', date: '2023-05-15', time: '11:30 AM', status: 'completed' },
    { id: 3, student: 'Alex Rodriguez', doctor: 'Dr. Sarah Johnson', date: '2023-05-16', time: '09:15 AM', status: 'cancelled' },
    { id: 4, student: 'Lisa Wang', doctor: 'Dr. Michael Chen', date: '2023-05-16', time: '02:00 PM', status: 'scheduled' }
  ];

  // Mock data for medical record statistics
  const medicalStats = {
    totalRecords: 156,
    recordsThisMonth: 32,
    upcomingFollowUps: 18,
    activeStudents: 87,
    commonDiagnoses: [
      { name: 'Common Cold', count: 28 },
      { name: 'Anxiety', count: 22 },
      { name: 'Allergic Reaction', count: 17 },
      { name: 'Sprained Ankle', count: 12 },
      { name: 'Insomnia', count: 10 }
    ],
    activeDoctors: [
      { name: 'Dr. Sarah Johnson', recordCount: 45 },
      { name: 'Dr. Michael Chen', recordCount: 38 },
      { name: 'Dr. Emily Wilson', recordCount: 32 },
      { name: 'Dr. Robert Davis', recordCount: 24 },
      { name: 'Dr. Lisa Patel', recordCount: 17 }
    ]
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Dashboard Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Admin Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Welcome back, {currentUser?.displayName || 'Admin'}
            </Typography>
          </Box>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<SettingsIcon />}
            onClick={() => navigate('/admin/settings')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            System Settings
          </Button>
        </Box>

        {/* System Statistics */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {systemStats.map((stat) => (
            <Grid item xs={12} sm={6} md={3} key={stat.id}>
              <Paper 
                sx={{ 
                  p: 3, 
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  height: '100%'
                }}
              >
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  {stat.title}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="h4" fontWeight="bold">
                    {stat.count}
                  </Typography>
                  <Chip 
                    icon={stat.trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
                    label={`${stat.change}%`}
                    color={stat.trend === 'up' ? 'success' : 'error'}
                    size="small"
                    sx={{ fontWeight: 'bold' }}
                  />
                </Box>
              </Paper>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" fontWeight="bold" sx={{ mb: 3 }}>
            Medical Records Overview
          </Typography>
          <MedicalRecordStats 
            totalRecords={medicalStats.totalRecords}
            recordsThisMonth={medicalStats.recordsThisMonth}
            upcomingFollowUps={medicalStats.upcomingFollowUps}
            activeStudents={medicalStats.activeStudents}
            commonDiagnoses={medicalStats.commonDiagnoses}
            activeDoctors={medicalStats.activeDoctors}
          />
        </Box>

        <Grid container spacing={3}>
          {/* Quick Actions */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Quick Actions
              </Typography>
              <Divider sx={{ my: 2 }} />
              <List sx={{ py: 0 }}>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/users')}
                >
                  <ListItemIcon>
                    <PeopleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Users" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/doctors')}
                >
                  <ListItemIcon>
                    <LocalHospitalIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Doctors" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/students')}
                >
                  <ListItemIcon>
                    <SchoolIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Students" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/appointments')}
                >
                  <ListItemIcon>
                    <CalendarMonthIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Manage Appointments" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/admin/reports')}
                >
                  <ListItemIcon>
                    <AssessmentIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Generate Reports" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Recent Users */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Recent Users
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/admin/users')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <TableContainer>
                <Table sx={{ minWidth: 650 }} aria-label="recent users table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Join Date</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentUsers.map((user) => (
                      <TableRow
                        key={user.id}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      >
                        <TableCell component="th" scope="row">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar 
                              sx={{ 
                                mr: 2, 
                                bgcolor: user.role === 'doctor' ? 'primary.main' : 'secondary.main' 
                              }}
                            >
                              {user.name.charAt(0)}
                            </Avatar>
                            <Typography variant="body2" fontWeight="medium">
                              {user.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip 
                            label={user.role.charAt(0).toUpperCase() + user.role.slice(1)} 
                            color={user.role === 'doctor' ? 'primary' : 'secondary'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={user.status.charAt(0).toUpperCase() + user.status.slice(1)} 
                            color={
                              user.status === 'active' ? 'success' : 
                              user.status === 'pending' ? 'warning' : 'error'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{user.joinDate}</TableCell>
                        <TableCell align="right">
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/admin/users/${user.id}`)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/admin/users/${user.id}/edit`)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>

          {/* Recent Appointments */}
          <Grid item xs={12}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                mt: 3,
                mb: 5
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Recent Appointments
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/admin/appointments')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <TableContainer>
                <Table sx={{ minWidth: 650 }} aria-label="recent appointments table">
                  <TableHead>
                    <TableRow>
                      <TableCell>Student</TableCell>
                      <TableCell>Doctor</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Time</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentAppointments.map((appointment) => (
                      <TableRow
                        key={appointment.id}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      >
                        <TableCell component="th" scope="row">
                          {appointment.student}
                        </TableCell>
                        <TableCell>{appointment.doctor}</TableCell>
                        <TableCell>{appointment.date}</TableCell>
                        <TableCell>{appointment.time}</TableCell>
                        <TableCell>
                          <Chip 
                            label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)} 
                            color={
                              appointment.status === 'scheduled' ? 'primary' : 
                              appointment.status === 'completed' ? 'success' : 'error'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/admin/appointments/${appointment.id}`)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/admin/appointments/${appointment.id}/edit`)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
        </Grid>

        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h2" fontWeight="bold">
              Recent Medical Records
            </Typography>
            <Button 
              variant="outlined" 
              onClick={() => navigate('/admin/medical-records')}
              sx={{ borderRadius: 2 }}
            >
              View All Records
            </Button>
          </Box>
          
          <Paper 
            sx={{ 
              borderRadius: 3,
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
            }}
          >
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                    <TableCell>Date</TableCell>
                    <TableCell>Student</TableCell>
                    <TableCell>Doctor</TableCell>
                    <TableCell>Diagnosis</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {medicalStats.activeDoctors.slice(0, 5).map((doctor, index) => (
                    <TableRow key={index} hover>
                      <TableCell>{new Date().toISOString().split('T')[0]}</TableCell>
                      <TableCell>
                        <Box 
                          sx={{ 
                            color: 'primary.main', 
                            cursor: 'pointer',
                            '&:hover': { textDecoration: 'underline' }
                          }}
                        >
                          Student {index + 1}
                        </Box>
                      </TableCell>
                      <TableCell>{doctor.name}</TableCell>
                      <TableCell>{medicalStats.commonDiagnoses[index].name}</TableCell>
                      <TableCell align="right">
                        <Button 
                          size="small" 
                          onClick={() => navigate(`/admin/medical-records/${index + 1}`)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default AdminDashboard;


