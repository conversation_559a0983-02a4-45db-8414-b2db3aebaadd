import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid,
  Divider,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EmailIcon from '@mui/icons-material/Email';
import PersonIcon from '@mui/icons-material/Person';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import EventIcon from '@mui/icons-material/Event';

const AdminUserDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [tabValue, setTabValue] = useState(0);
  
  // Mock user data - in a real app, this would come from an API
  const [user] = useState({
    id: parseInt(id),
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    joinDate: '2023-01-05',
    lastLogin: '2024-01-15 10:30 AM',
    avatar: 'JD',
    permissions: ['User Management', 'Doctor Management', 'Student Management', 'Reports', 'Settings'],
    recentActivity: [
      { action: 'Updated user profile', date: '2024-01-15 10:30 AM' },
      { action: 'Generated monthly report', date: '2024-01-14 3:45 PM' },
      { action: 'Added new doctor', date: '2024-01-13 2:15 PM' },
      { action: 'Modified system settings', date: '2024-01-12 11:20 AM' }
    ]
  });

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin': return 'error';
      case 'doctor': return 'primary';
      case 'student': return 'secondary';
      default: return 'default';
    }
  };
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            User Details
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/admin/users')}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              Back to Users
            </Button>
            <Button 
              variant="contained" 
              startIcon={<EditIcon />}
              onClick={() => navigate(`/admin/users/${id}/edit`)}
              sx={{ borderRadius: 2 }}
            >
              Edit User
            </Button>
          </Box>
        </Box>
        
        {/* User Profile */}
        <Paper 
          sx={{ 
            p: 4, 
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={4}>
            {/* Avatar and Basic Info */}
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar 
                sx={{ 
                  width: 150, 
                  height: 150, 
                  fontSize: '3rem',
                  mb: 2,
                  bgcolor: getRoleColor(user.role) + '.main'
                }}
              >
                {generateAvatar(user.name)}
              </Avatar>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {user.name}
              </Typography>
              <Chip 
                label={user.status === 'active' ? 'Active' : 'Inactive'} 
                color={user.status === 'active' ? 'success' : 'default'}
                variant="outlined"
                sx={{ mb: 1 }}
              />
              <Chip 
                label={user.role.charAt(0).toUpperCase() + user.role.slice(1)} 
                color={getRoleColor(user.role)}
                sx={{ mb: 3 }}
              />
            </Grid>

            {/* User Information */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                User Information
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {user.email}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PersonIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Role
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <EventIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Join Date
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {new Date(user.joinDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AdminPanelSettingsIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Last Login
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {user.lastLogin}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Paper>

        {/* Tabs for additional information */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
          >
            <Tab label="Permissions" />
            <Tab label="Recent Activity" />
          </Tabs>
          
          <Box sx={{ p: 4 }}>
            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  User Permissions
                </Typography>
                <Grid container spacing={2}>
                  {user.permissions.map((permission, index) => (
                    <Grid item key={index}>
                      <Chip 
                        label={permission} 
                        variant="outlined" 
                        color="primary"
                        sx={{ mb: 1 }}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
            
            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Recent Activity
                </Typography>
                <List>
                  {user.recentActivity.map((activity, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <EventIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.action}
                        secondary={activity.date}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminUserDetail;
