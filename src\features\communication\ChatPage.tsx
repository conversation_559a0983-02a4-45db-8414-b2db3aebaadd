
import React, { useState, useEffect, useRef } from 'react';
import { 
  Container, Typography, Box, Paper, TextField, Button, 
  List, ListItem, ListItemText, Avatar, Divider, Grid,
  CircularProgress, IconButton, Badge, Chip, Tab, Tabs
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import VideocamIcon from '@mui/icons-material/Videocam';
import VideocamOffIcon from '@mui/icons-material/VideocamOff';
import PhoneIcon from '@mui/icons-material/Phone';
import CallEndIcon from '@mui/icons-material/CallEnd';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import SearchIcon from '@mui/icons-material/Search';
import { useFirebase } from '../../contexts/FirebaseContext';
import { getAssignedDoctors, getAllDoctors, requestDoctorAssignment, type AssignedDoctor, type Doctor } from '../../services/doctorAssignmentService';

// Add CSS animations for the call overlays
const callAnimations = `
  @keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes avatarPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
    50% { transform: scale(1.05); box-shadow: 0 12px 40px rgba(0,0,0,0.4); }
  }

  @keyframes soundWave {
    0% { opacity: 1; transform: translate(-50%, -50%) scale(0.8); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(1.2); }
  }

  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }
`;

// Inject animations into the document
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = callAnimations;
  document.head.appendChild(styleSheet);
}
import Layout from '../../components/layout/Layout';

// Mock data for doctors
const doctors = [
  { id: 'd1', name: 'Dr. Sarah Johnson', specialty: 'General Practitioner', avatar: '/doctor1.jpg', online: true },
  { id: 'd2', name: 'Dr. Michael Chen', specialty: 'Cardiologist', avatar: '/doctor2.jpg', online: false },
  { id: 'd3', name: 'Dr. Emily Rodriguez', specialty: 'Dermatologist', avatar: '/doctor3.jpg', online: true },
  { id: 'd4', name: 'Dr. James Wilson', specialty: 'Neurologist', avatar: '/doctor4.jpg', online: true },
  { id: 'd5', name: 'Dr. Lisa Thompson', specialty: 'Psychiatrist', avatar: '/doctor5.jpg', online: false }
];

const ChatPage = () => {
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedDoctor, setSelectedDoctor] = useState<AssignedDoctor | Doctor | null>(null);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(false);
  const [allDoctors, setAllDoctors] = useState<Doctor[]>([]);
  const [assignedDoctors, setAssignedDoctors] = useState<AssignedDoctor[]>([]);
  const [showAllProviders, setShowAllProviders] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isVideoCallActive, setIsVideoCallActive] = useState(false);
  const [isVoiceCallActive, setIsVoiceCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { currentUser } = useFirebase();

  useEffect(() => {
    // Load assigned doctors from the assignment service or show mock data
    const mockAssignedDoctors = [
      {
        id: 1,
        name: 'Dr. Sarah Johnson',
        specialty: 'General Practice',
        department: 'Primary Care',
        avatar: '',
        online: true,
        assigned: true,
        assignedDate: new Date().toISOString(),
        lastMessage: 'Your test results came back normal. Let\'s schedule a follow-up next week.',
        lastMessageTime: '2 min ago',
        unreadCount: 2
      },
      {
        id: 2,
        name: 'Dr. Michael Chen',
        specialty: 'Mental Health',
        department: 'Counseling Services',
        avatar: '',
        online: true,
        assigned: true,
        assignedDate: new Date().toISOString(),
        lastMessage: 'How are you feeling after starting the new medication?',
        lastMessageTime: '15 min ago',
        unreadCount: 1
      },
      {
        id: 3,
        name: 'Dr. Emily Rodriguez',
        specialty: 'Emergency Medicine',
        department: 'Emergency Care',
        avatar: '',
        online: false,
        assigned: true,
        assignedDate: new Date().toISOString(),
        lastMessage: 'Remember to take your medication with food. Any side effects?',
        lastMessageTime: '1 hour ago',
        unreadCount: 0
      },
      {
        id: 4,
        name: 'Dr. David Kim',
        specialty: 'Internal Medicine',
        department: 'Internal Medicine',
        avatar: '',
        online: true,
        assigned: true,
        assignedDate: new Date().toISOString(),
        lastMessage: 'Your appointment is confirmed for tomorrow at 2 PM.',
        lastMessageTime: '3 hours ago',
        unreadCount: 3
      },
      {
        id: 5,
        name: 'Dr. Lisa Thompson',
        specialty: 'Dermatology',
        department: 'Dermatology',
        avatar: '',
        online: false,
        assigned: true,
        assignedDate: new Date().toISOString(),
        lastMessage: 'I\'ve updated your treatment plan. Please review the attached document.',
        lastMessageTime: '1 day ago',
        unreadCount: 0
      }
    ];

    if (currentUser) {
      const assigned = getAssignedDoctors(currentUser.uid);
      const all = getAllDoctors();

      // Use real assigned doctors if available, otherwise use mock data
      const finalAssigned = assigned.length > 0 ? assigned : mockAssignedDoctors;

      // Mark assigned doctors in the all doctors list
      const allWithAssignmentStatus = all.map(doctor => ({
        ...doctor,
        assigned: finalAssigned.some(assignedDoc => assignedDoc.id === doctor.id),
        requested: false
      }));

      setAssignedDoctors(finalAssigned);
      setAllDoctors(allWithAssignmentStatus);
      setLoading(false);

      console.log(`📋 Loaded ${finalAssigned.length} assigned doctors`);
    } else {
      // Show mock data even without login for demo purposes
      setAssignedDoctors(mockAssignedDoctors);
      setAllDoctors(getAllDoctors());
      setLoading(false);
    }
  }, [currentUser]);

  useEffect(() => {
    // Only scroll to bottom when explicitly requested (new messages sent/received)
    if (shouldScrollToBottom && !loading && messages.length > 0) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      setShouldScrollToBottom(false);
    }
  }, [messages, loading, shouldScrollToBottom]);

  const handleSelectDoctor = (doctor) => {
    setSelectedDoctor(doctor);
    setLoading(true);

    // Simulate loading chat history
    setTimeout(() => {
      let chatHistory = [];

      if (doctor.id === 'd1') {
        chatHistory = [
          { id: 1, sender: 'doctor', text: 'Hello! How can I help you today?', time: '10:30 AM' },
          { id: 2, sender: 'user', text: 'I\'ve been having headaches for the past few days.', time: '10:32 AM' },
          { id: 3, sender: 'doctor', text: 'I\'m sorry to hear that. How severe are they on a scale of 1-10?', time: '10:33 AM' },
          { id: 4, sender: 'user', text: 'About a 6 or 7. They\'re worse in the morning.', time: '10:35 AM' },
          { id: 5, sender: 'doctor', text: 'Are you experiencing any other symptoms like nausea or sensitivity to light?', time: '10:36 AM' },
          { id: 6, sender: 'user', text: 'Yes, some sensitivity to light and occasional nausea.', time: '10:38 AM' },
          { id: 7, sender: 'doctor', text: 'Based on your symptoms, this could be tension headaches or migraines. I\'d like to schedule you for a follow-up and possibly some tests.', time: '10:40 AM' }
        ];
      } else if (doctor.id === 'd2') {
        chatHistory = [
          { id: 1, sender: 'doctor', text: 'Good afternoon. How have you been feeling since our last appointment?', time: '2:15 PM' },
          { id: 2, sender: 'user', text: 'I\'ve been following the medication regimen you prescribed.', time: '2:17 PM' },
          { id: 3, sender: 'doctor', text: 'Excellent. Have you noticed any improvement in your symptoms?', time: '2:18 PM' },
          { id: 4, sender: 'user', text: 'Yes, my chest pain has reduced significantly and I feel less short of breath.', time: '2:20 PM' },
          { id: 5, sender: 'doctor', text: 'That\'s great news! Are you experiencing any side effects from the medication?', time: '2:21 PM' },
          { id: 6, sender: 'user', text: 'Just some mild dizziness when I stand up quickly.', time: '2:23 PM' },
          { id: 7, sender: 'doctor', text: 'That\'s normal with blood pressure medication. Make sure to stand up slowly and stay hydrated.', time: '2:24 PM' }
        ];
      } else if (doctor.id === 'd3') {
        chatHistory = [
          { id: 1, sender: 'doctor', text: 'Hi there! Have you been applying the prescribed cream?', time: '11:45 AM' },
          { id: 2, sender: 'user', text: 'Yes, twice daily as recommended.', time: '11:47 AM' },
          { id: 3, sender: 'doctor', text: 'Great! Has there been any improvement in the rash?', time: '11:48 AM' },
          { id: 4, sender: 'user', text: 'It seems to be less red, but still itchy sometimes.', time: '11:50 AM' },
          { id: 5, sender: 'doctor', text: 'That\'s progress! Continue with the treatment and let\'s check again in a few days.', time: '11:52 AM' },
          { id: 6, sender: 'user', text: 'Should I be concerned about the itching?', time: '11:54 AM' },
          { id: 7, sender: 'doctor', text: 'Some itching is normal as the skin heals. If it becomes severe or you develop new symptoms, contact me immediately.', time: '11:55 AM' }
        ];
      } else if (doctor.id === 'd4') {
        chatHistory = [
          { id: 1, sender: 'doctor', text: 'Hello! I reviewed your MRI results from last week.', time: '9:15 AM' },
          { id: 2, sender: 'user', text: 'What did you find? I\'m a bit nervous.', time: '9:17 AM' },
          { id: 3, sender: 'doctor', text: 'The good news is that there are no signs of any serious neurological conditions.', time: '9:18 AM' },
          { id: 4, sender: 'user', text: 'That\'s such a relief! What about my memory issues?', time: '9:20 AM' },
          { id: 5, sender: 'doctor', text: 'Your symptoms appear to be stress-related. I recommend stress management techniques and regular sleep.', time: '9:22 AM' }
        ];
      } else if (doctor.id === 'd5') {
        chatHistory = [
          { id: 1, sender: 'doctor', text: 'How have you been feeling since we started the new treatment plan?', time: '3:30 PM' },
          { id: 2, sender: 'user', text: 'I think the anxiety medication is helping. I feel more balanced.', time: '3:32 PM' },
          { id: 3, sender: 'doctor', text: 'That\'s wonderful to hear. Are you still practicing the breathing exercises we discussed?', time: '3:33 PM' },
          { id: 4, sender: 'user', text: 'Yes, especially when I feel overwhelmed at work.', time: '3:35 PM' },
          { id: 5, sender: 'doctor', text: 'Perfect! Remember, recovery is a process. You\'re making excellent progress.', time: '3:36 PM' }
        ];
      } else {
        // Default chat for other doctors
        chatHistory = [
          { id: 1, sender: 'doctor', text: `Hello! I'm ${doctor.name}. How can I assist you today?`, time: '10:00 AM' }
        ];
      }

      setMessages(chatHistory);
      setLoading(false);

      // Mark messages as read for assigned doctors
      if ('unreadCount' in doctor && doctor.unreadCount > 0) {
        // Update the assigned doctors list to mark as read
        setAssignedDoctors(prev =>
          prev.map(d =>
            d.id === doctor.id ? { ...d, unreadCount: 0 } : d
          )
        );
      }
    }, 800);
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedDoctor) return;

    const message = {
      id: Date.now(),
      sender: 'user',
      text: newMessage,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages([...messages, message]);
    setNewMessage('');
    setShouldScrollToBottom(true); // Trigger scroll for new message

    // Simulate doctor response after a delay
    setTimeout(() => {
      const doctorResponse = {
        id: Date.now() + 1,
        sender: 'doctor',
        text: 'Thank you for sharing that information. I\'ll take it into consideration. Is there anything else you\'d like to discuss?',
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      setMessages(prev => [...prev, doctorResponse]);
      setShouldScrollToBottom(true); // Trigger scroll for doctor response
    }, 2000);
  };

  const handleRequestProvider = async (doctor: Doctor) => {
    if (!currentUser) return;

    // Update UI immediately to show request is processing
    const updatedAllDoctors = allDoctors.map(d =>
      d.id === doctor.id ? { ...d, requested: true } : d
    );
    setAllDoctors(updatedAllDoctors);

    try {
      const success = await requestDoctorAssignment(currentUser.uid, doctor.id);
      if (success) {
        alert(`✅ Request sent to connect with ${doctor.name}! You'll be notified when approved.`);
      } else {
        alert(`❌ Failed to send request. Please try again.`);
        // Revert UI change on failure
        const revertedDoctors = allDoctors.map(d =>
          d.id === doctor.id ? { ...d, requested: false } : d
        );
        setAllDoctors(revertedDoctors);
      }
    } catch (error) {
      console.error('Error requesting doctor assignment:', error);
      alert(`❌ Failed to send request. Please try again.`);
    }
  };

  // Video/Voice call handlers
  const handleStartVideoCall = () => {
    setIsVideoCallActive(true);
    setIsVoiceCallActive(false);
  };

  const handleStartVoiceCall = () => {
    setIsVoiceCallActive(true);
    setIsVideoCallActive(false);
  };

  const handleEndCall = () => {
    setIsVideoCallActive(false);
    setIsVoiceCallActive(false);
    setIsMuted(false);
    setIsVideoEnabled(true);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const toggleVideo = () => {
    setIsVideoEnabled(!isVideoEnabled);
  };

  // Filter doctors based on search term
  const filteredAssignedDoctors = assignedDoctors.filter(doctor =>
    doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAllDoctors = allDoctors.filter(doctor =>
    doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderDoctorsList = () => (
    <Paper sx={{
      p: 2,
      height: '70vh',
      maxHeight: '70vh',
      borderRadius: 3,
      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" fontWeight="bold">
          Healthcare Providers
        </Typography>
        <Button 
          size="small"
          color="primary"
          onClick={() => setShowAllProviders(!showAllProviders)}
        >
          {showAllProviders ? 'Show Assigned' : 'Browse All'}
        </Button>
      </Box>
      <Divider sx={{ mb: 2 }} />

      {/* Search Bar */}
      <TextField
        fullWidth
        size="small"
        placeholder="Search doctors by name or specialty..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        InputProps={{
          startAdornment: (
            <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
          ),
        }}
        sx={{
          mb: 2,
          '& .MuiOutlinedInput-root': {
            borderRadius: 2
          }
        }}
      />

      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {showAllProviders ? (
          <>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Browse all available healthcare providers. Request to connect with specialists not currently assigned to you.
            </Typography>
            <List>
            {filteredAllDoctors.map((doctor) => (
              <ListItem 
                key={doctor.id}
                button={doctor.assigned}
                onClick={doctor.assigned ? () => handleSelectDoctor(doctor) : undefined}
                sx={{ 
                  borderRadius: 2,
                  mb: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'primary.main'
                    },
                    '& .MuiListItemText-primary': {
                      color: 'white !important'
                    },
                    '& .MuiListItemText-secondary': {
                      color: 'white !important'
                    },
                    '& .MuiListItemText-secondary *': {
                      color: 'white !important'
                    },
                    '& .MuiSvgIcon-root': {
                      color: 'white !important'
                    },
                    '& .MuiAvatar-root': {
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      color: 'white'
                    }
                  },
                  '&:hover': doctor.assigned ? {
                    backgroundColor: 'rgba(0, 114, 255, 0.04)'
                  } : {}
                }}
              >
                <Avatar 
                  alt={doctor.name} 
                  src={doctor.avatar}
                  sx={{ mr: 2 }}
                >
                  {doctor.name.charAt(0)}
                </Avatar>
                <ListItemText 
                  primary={doctor.name}
                  secondary={
                    <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                      <Typography variant="body2" component="span">
                        {doctor.specialty}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                        <FiberManualRecordIcon 
                          sx={{ 
                            color: doctor.online ? 'success.main' : 'text.disabled', 
                            fontSize: 8,
                            mr: 0.5
                          }} 
                        />
                        <Typography variant="caption" component="span">
                          {doctor.online ? 'Online' : 'Offline'}
                        </Typography>
                      </Box>
                    </Box>
                  }
                  primaryTypographyProps={{
                    fontWeight: selectedDoctor?.id === doctor.id ? 'bold' : 'medium'
                  }}
                />
                {!doctor.assigned && (
                  <Button 
                    size="small" 
                    variant="outlined"
                    color="primary"
                    disabled={doctor.requested}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRequestProvider(doctor);
                    }}
                    sx={{ ml: 1, minWidth: 100 }}
                  >
                    {doctor.requested ? 'Requested' : 'Request'}
                  </Button>
                )}
                {doctor.assigned && (
                  <Chip 
                    size="small" 
                    label="Assigned" 
                    color="primary" 
                    variant="outlined"
                    sx={{ ml: 1 }}
                  />
                )}
              </ListItem>
            ))}
          </List>
        </>
      ) : (
        <>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            These healthcare providers are assigned to you. Click on a provider to start a conversation.
          </Typography>
          <List>
            {filteredAssignedDoctors.map((doctor) => (
              <ListItem 
                key={doctor.id}
                button
                selected={selectedDoctor?.id === doctor.id}
                onClick={() => handleSelectDoctor(doctor)}
                sx={{ 
                  borderRadius: 2,
                  mb: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'primary.main'
                    },
                    '& .MuiListItemText-primary': {
                      color: 'white !important'
                    },
                    '& .MuiListItemText-secondary': {
                      color: 'white !important'
                    },
                    '& .MuiListItemText-secondary *': {
                      color: 'white !important'
                    },
                    '& .MuiSvgIcon-root': {
                      color: 'white !important'
                    }
                  },
                  '&:hover': {
                    backgroundColor: 'rgba(0, 114, 255, 0.04)'
                  }
                }}
              >
                <Avatar 
                  alt={doctor.name} 
                  src={doctor.avatar}
                  sx={{ mr: 2 }}
                >
                  {doctor.name.charAt(0)}
                </Avatar>
                <ListItemText
                  primary={doctor.name}
                  secondary={
                    <Box>
                      <Typography variant="body2" component="div" noWrap sx={{ mb: 0.5 }}>
                        {doctor.specialty}
                      </Typography>
                      <Typography variant="body2" component="div" noWrap sx={{ mb: 0.5, opacity: 0.8 }}>
                        {doctor.lastMessage}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <FiberManualRecordIcon
                            sx={{
                              color: doctor.online ? 'success.main' : 'text.disabled',
                              fontSize: 8,
                              mr: 0.5
                            }}
                          />
                          <Typography variant="caption" component="span">
                            {doctor.online ? 'Online' : 'Offline'}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="caption" color="text.secondary">
                            {doctor.lastMessageTime}
                          </Typography>
                          {(doctor.unreadCount || 0) > 0 && (
                            <Chip
                              label={doctor.unreadCount}
                              size="small"
                              color="error"
                              sx={{
                                height: 20,
                                fontSize: '0.75rem',
                                fontWeight: 'bold',
                                minWidth: 20
                              }}
                            />
                          )}
                        </Box>
                      </Box>
                    </Box>
                  }
                  primaryTypographyProps={{
                    fontWeight: selectedDoctor?.id === doctor.id ? 'bold' : 'medium'
                  }}
                />
              </ListItem>
            ))}
          </List>
          {assignedDoctors.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                You don't have any assigned healthcare providers yet.
              </Typography>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={() => setShowAllProviders(true)}
                sx={{ mt: 2 }}
              >
                Browse Providers
              </Button>
            </Box>
          )}
          </>
        )}
      </Box>
    </Paper>
  );

  const renderChatArea = () => (
    <Paper sx={{ p: 0, height: '70vh', display: 'flex', flexDirection: 'column', borderRadius: 2, overflow: 'hidden' }}>
      {selectedDoctor ? (
        <>
          {/* Chat Header */}
          <Box sx={{ 
            p: 2, 
            backgroundColor: 'primary.main', 
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar 
                alt={selectedDoctor.name} 
                src={selectedDoctor.avatar}
                sx={{ mr: 2 }}
              >
                {selectedDoctor.name.charAt(0)}
              </Avatar>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  {selectedDoctor.name}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <FiberManualRecordIcon 
                    sx={{ 
                      color: selectedDoctor.online ? 'success.light' : 'text.disabled', 
                      fontSize: 12,
                      mr: 0.5
                    }} 
                  />
                  <Typography variant="caption">
                    {selectedDoctor.online ? 'Online' : 'Offline'}
                  </Typography>
                </Box>
              </Box>
            </Box>
            <Box>
              <IconButton color="inherit" size="small" sx={{ mr: 1 }}>
                <PhoneIcon />
              </IconButton>
              <IconButton color="inherit" size="small">
                <VideocamIcon />
              </IconButton>
            </Box>
          </Box>
          
          {/* Messages Area */}
          {loading ? (
            <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2, backgroundColor: '#f5f5f5' }}>
              <List>
                {messages.map((message) => (
                  <ListItem
                    key={message.id}
                    sx={{
                      textAlign: message.sender === 'user' ? 'right' : 'left',
                      mb: 2,
                      px: 2
                    }}
                  >
                    <Grid container spacing={2} direction={message.sender === 'user' ? 'row-reverse' : 'row'}>
                      <Grid item xs={1}>
                        <Avatar
                          sx={{
                            bgcolor: message.sender === 'user' ? 'primary.main' : 'secondary.main',
                          }}
                        >
                          {message.sender === 'user' ? currentUser?.email?.charAt(0).toUpperCase() || 'U' : selectedDoctor.name.charAt(0)}
                        </Avatar>
                      </Grid>
                      <Grid item xs={11} sm={8} md={7}>
                        <Paper
                          elevation={1}
                          sx={{
                            p: 2,
                            bgcolor: message.sender === 'user' ? 'primary.light' : 'white',
                            color: message.sender === 'user' ? 'white' : 'text.primary',
                            borderRadius: message.sender === 'user'
                              ? '20px 20px 5px 20px'
                              : '20px 20px 20px 5px',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                          }}
                        >
                          <Typography variant="body1">{message.text}</Typography>
                          <Typography variant="caption" sx={{ display: 'block', mt: 1, textAlign: 'right', opacity: 0.8 }}>
                            {message.time}
                          </Typography>
                        </Paper>
                      </Grid>
                    </Grid>
                  </ListItem>
                ))}
                <div ref={messagesEndRef} />
              </List>
            </Box>
          )}
          
          {/* Message Input */}
          <Box sx={{ p: 2, backgroundColor: 'background.paper', borderTop: '1px solid', borderColor: 'divider' }}>
            <form onSubmit={handleSendMessage}>
              <Grid container spacing={1}>
                <Grid item xs={11}>
                  <TextField
                    fullWidth
                    placeholder="Type a message"
                    variant="outlined"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={1}>
                  <Button 
                    variant="contained" 
                    color="primary"
                    type="submit"
                    disabled={!newMessage.trim()}
                    sx={{ minWidth: 0, p: 1, borderRadius: 2, height: '100%' }}
                  >
                    <SendIcon />
                  </Button>
                </Grid>
              </Grid>
            </form>
          </Box>
        </>
      ) : (
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100%',
          p: 3,
          textAlign: 'center'
        }}>
          <Typography variant="h6" gutterBottom>
            Welcome to the Chat Portal
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            Select a healthcare provider from the list to start a conversation
          </Typography>
          <img 
            src="/chat-illustration.svg" 
            alt="Chat illustration" 
            style={{ maxWidth: '60%', opacity: 0.7 }}
          />
        </Box>
      )}
    </Paper>
  );

  return (
    <Layout>
      <Box sx={{ 
        bgcolor: '#f5f7fa', 
        minHeight: '100vh',
        py: 4
      }}>
        <Container maxWidth="lg">
          {/* Updated header styling to match dashboard */}
          <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                Communication Portal
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Connect with healthcare providers through secure messaging
              </Typography>
            </Box>
          </Box>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              {renderDoctorsList()}
            </Grid>
            <Grid item xs={12} md={8}>
              <Paper sx={{
                p: 0,
                height: '70vh',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 3,
                overflow: 'hidden',
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                position: 'relative'
              }}>
                {selectedDoctor ? (
                  <>
                    {/* Chat Header */}
                    <Box sx={{ 
                      p: 2, 
                      backgroundColor: 'primary.main', 
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar 
                          alt={selectedDoctor.name} 
                          src={selectedDoctor.avatar}
                          sx={{ mr: 2 }}
                        >
                          {selectedDoctor.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {selectedDoctor.name}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <FiberManualRecordIcon 
                              sx={{ 
                                color: selectedDoctor.online ? 'success.light' : 'text.disabled', 
                                fontSize: 12,
                                mr: 0.5
                              }} 
                            />
                            <Typography variant="caption">
                              {selectedDoctor.online ? 'Online' : 'Offline'}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      <Box>
                        <IconButton
                          color="inherit"
                          size="small"
                          sx={{ mr: 1 }}
                          onClick={handleStartVoiceCall}
                          disabled={isVideoCallActive || isVoiceCallActive}
                        >
                          <PhoneIcon />
                        </IconButton>
                        <IconButton
                          color="inherit"
                          size="small"
                          onClick={handleStartVideoCall}
                          disabled={isVideoCallActive || isVoiceCallActive}
                        >
                          <VideocamIcon />
                        </IconButton>
                      </Box>
                    </Box>

                    {/* Video/Voice Call Overlay */}
                    {(isVideoCallActive || isVoiceCallActive) && (
                      <Box sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: isVideoCallActive
                          ? 'rgba(0,0,0,0.95)'
                          : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        zIndex: 1000,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        borderRadius: 3,
                        overflow: 'hidden',
                        '&::before': isVoiceCallActive ? {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: 'radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)',
                          animation: 'pulse 3s ease-in-out infinite'
                        } : {}
                      }}>
                        {/* Animated Background Elements for Voice Call */}
                        {isVoiceCallActive && (
                          <>
                            <Box sx={{
                              position: 'absolute',
                              top: '20%',
                              left: '10%',
                              width: 60,
                              height: 60,
                              borderRadius: '50%',
                              background: 'rgba(255,255,255,0.1)',
                              animation: 'float 6s ease-in-out infinite'
                            }} />
                            <Box sx={{
                              position: 'absolute',
                              top: '60%',
                              right: '15%',
                              width: 40,
                              height: 40,
                              borderRadius: '50%',
                              background: 'rgba(255,255,255,0.08)',
                              animation: 'float 4s ease-in-out infinite reverse'
                            }} />
                            <Box sx={{
                              position: 'absolute',
                              bottom: '20%',
                              left: '20%',
                              width: 80,
                              height: 80,
                              borderRadius: '50%',
                              background: 'rgba(255,255,255,0.05)',
                              animation: 'float 8s ease-in-out infinite'
                            }} />
                          </>
                        )}

                        {/* Call Status */}
                        <Box sx={{ textAlign: 'center', mb: 4, zIndex: 1 }}>
                          {/* Doctor Avatar with Pulse Animation for Voice Call */}
                          <Box sx={{ position: 'relative', display: 'inline-block', mb: 3 }}>
                            <Avatar sx={{
                              width: 140,
                              height: 140,
                              bgcolor: 'rgba(255,255,255,0.2)',
                              border: '4px solid rgba(255,255,255,0.3)',
                              fontSize: '3.5rem',
                              boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
                              animation: isVoiceCallActive ? 'avatarPulse 2s ease-in-out infinite' : 'none'
                            }}>
                              {selectedDoctor.name.charAt(0)}
                            </Avatar>

                            {/* Voice Call Sound Waves */}
                            {isVoiceCallActive && (
                              <>
                                <Box sx={{
                                  position: 'absolute',
                                  top: '50%',
                                  left: '50%',
                                  transform: 'translate(-50%, -50%)',
                                  width: 180,
                                  height: 180,
                                  border: '2px solid rgba(255,255,255,0.3)',
                                  borderRadius: '50%',
                                  animation: 'soundWave 2s ease-out infinite'
                                }} />
                                <Box sx={{
                                  position: 'absolute',
                                  top: '50%',
                                  left: '50%',
                                  transform: 'translate(-50%, -50%)',
                                  width: 220,
                                  height: 220,
                                  border: '2px solid rgba(255,255,255,0.2)',
                                  borderRadius: '50%',
                                  animation: 'soundWave 2s ease-out infinite 0.5s'
                                }} />
                                <Box sx={{
                                  position: 'absolute',
                                  top: '50%',
                                  left: '50%',
                                  transform: 'translate(-50%, -50%)',
                                  width: 260,
                                  height: 260,
                                  border: '2px solid rgba(255,255,255,0.1)',
                                  borderRadius: '50%',
                                  animation: 'soundWave 2s ease-out infinite 1s'
                                }} />
                              </>
                            )}
                          </Box>

                          <Typography variant="h4" fontWeight="bold" gutterBottom sx={{
                            textShadow: '0 2px 10px rgba(0,0,0,0.3)',
                            mb: 1
                          }}>
                            {selectedDoctor.name}
                          </Typography>

                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mb: 2,
                            gap: 1
                          }}>
                            <Box sx={{
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              bgcolor: 'success.light',
                              animation: 'blink 1.5s ease-in-out infinite'
                            }} />
                            <Typography variant="h6" sx={{
                              opacity: 0.9,
                              fontWeight: 500,
                              textShadow: '0 1px 5px rgba(0,0,0,0.2)'
                            }}>
                              {isVideoCallActive ? '📹 Video Call' : '🎙️ Voice Call'}
                            </Typography>
                          </Box>

                          <Typography variant="body1" sx={{
                            opacity: 0.8,
                            fontSize: '1.1rem',
                            textShadow: '0 1px 3px rgba(0,0,0,0.2)'
                          }}>
                            Connected and secure
                          </Typography>
                        </Box>

                        {/* Video Call Preview (Mock) */}
                        {isVideoCallActive && (
                          <Box sx={{
                            position: 'absolute',
                            top: 20,
                            right: 20,
                            width: 200,
                            height: 150,
                            bgcolor: 'rgba(0,0,0,0.5)',
                            borderRadius: 2,
                            border: '2px solid rgba(255,255,255,0.3)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <Typography variant="body2" sx={{ opacity: 0.7 }}>
                              Your Video
                            </Typography>
                          </Box>
                        )}

                        {/* Call Duration */}
                        <Box sx={{
                          position: 'absolute',
                          top: 20,
                          left: 20,
                          bgcolor: 'rgba(0,0,0,0.6)',
                          px: 3,
                          py: 1.5,
                          borderRadius: 3,
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255,255,255,0.1)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
                        }}>
                          <Typography variant="body2" sx={{
                            fontWeight: 600,
                            fontSize: '0.95rem',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                          }}>
                            <Box sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: 'error.main',
                              animation: 'blink 1s ease-in-out infinite'
                            }} />
                            00:45
                          </Typography>
                        </Box>

                        {/* Call Controls */}
                        <Box sx={{
                          display: 'flex',
                          gap: 2,
                          mt: 4,
                          bgcolor: 'rgba(0,0,0,0.4)',
                          p: 3,
                          borderRadius: 4,
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255,255,255,0.1)',
                          boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
                          zIndex: 1
                        }}>
                          {/* Mute/Unmute Button */}
                          <Box sx={{ textAlign: 'center' }}>
                            <IconButton
                              onClick={toggleMute}
                              sx={{
                                bgcolor: isMuted ? 'error.main' : 'rgba(255,255,255,0.15)',
                                color: 'white',
                                width: 70,
                                height: 70,
                                mb: 1,
                                border: '2px solid rgba(255,255,255,0.2)',
                                '&:hover': {
                                  bgcolor: isMuted ? 'error.dark' : 'rgba(255,255,255,0.25)',
                                  transform: 'scale(1.1)',
                                  boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
                                },
                                transition: 'all 0.3s ease',
                                boxShadow: isMuted ? '0 0 20px rgba(244, 67, 54, 0.5)' : '0 4px 15px rgba(0,0,0,0.2)'
                              }}
                            >
                              {isMuted ? <MicOffIcon fontSize="large" /> : <MicIcon fontSize="large" />}
                            </IconButton>
                            <Typography variant="caption" sx={{
                              display: 'block',
                              color: 'rgba(255,255,255,0.8)',
                              fontWeight: 500
                            }}>
                              {isMuted ? 'Unmute' : 'Mute'}
                            </Typography>
                          </Box>

                          {/* Video Toggle (only for video calls) */}
                          {isVideoCallActive && (
                            <Box sx={{ textAlign: 'center' }}>
                              <IconButton
                                onClick={toggleVideo}
                                sx={{
                                  bgcolor: !isVideoEnabled ? 'error.main' : 'rgba(255,255,255,0.15)',
                                  color: 'white',
                                  width: 70,
                                  height: 70,
                                  mb: 1,
                                  border: '2px solid rgba(255,255,255,0.2)',
                                  '&:hover': {
                                    bgcolor: !isVideoEnabled ? 'error.dark' : 'rgba(255,255,255,0.25)',
                                    transform: 'scale(1.1)',
                                    boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
                                  },
                                  transition: 'all 0.3s ease',
                                  boxShadow: !isVideoEnabled ? '0 0 20px rgba(244, 67, 54, 0.5)' : '0 4px 15px rgba(0,0,0,0.2)'
                                }}
                              >
                                {isVideoEnabled ? <VideocamIcon fontSize="large" /> : <VideocamOffIcon fontSize="large" />}
                              </IconButton>
                              <Typography variant="caption" sx={{
                                display: 'block',
                                color: 'rgba(255,255,255,0.8)',
                                fontWeight: 500
                              }}>
                                {isVideoEnabled ? 'Stop Video' : 'Start Video'}
                              </Typography>
                            </Box>
                          )}

                          {/* End Call Button */}
                          <Box sx={{ textAlign: 'center' }}>
                            <IconButton
                              onClick={handleEndCall}
                              sx={{
                                bgcolor: 'error.main',
                                color: 'white',
                                width: 70,
                                height: 70,
                                mb: 1,
                                border: '2px solid rgba(255,255,255,0.2)',
                                '&:hover': {
                                  bgcolor: 'error.dark',
                                  transform: 'scale(1.1)',
                                  boxShadow: '0 4px 25px rgba(244, 67, 54, 0.6)'
                                },
                                transition: 'all 0.3s ease',
                                boxShadow: '0 0 25px rgba(244, 67, 54, 0.4)'
                              }}
                            >
                              <CallEndIcon fontSize="large" />
                            </IconButton>
                            <Typography variant="caption" sx={{
                              display: 'block',
                              color: 'rgba(255,255,255,0.8)',
                              fontWeight: 500
                            }}>
                              End Call
                            </Typography>
                          </Box>
                        </Box>

                        {/* Call Quality Indicator */}
                        <Box sx={{
                          position: 'absolute',
                          bottom: 20,
                          left: 20,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1.5,
                          bgcolor: 'rgba(0,0,0,0.6)',
                          px: 3,
                          py: 1.5,
                          borderRadius: 3,
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255,255,255,0.1)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
                        }}>
                          {/* Signal Strength Bars */}
                          <Box sx={{ display: 'flex', alignItems: 'end', gap: 0.5 }}>
                            <Box sx={{
                              width: 3,
                              height: 8,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite'
                            }} />
                            <Box sx={{
                              width: 3,
                              height: 12,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite 0.2s'
                            }} />
                            <Box sx={{
                              width: 3,
                              height: 16,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite 0.4s'
                            }} />
                            <Box sx={{
                              width: 3,
                              height: 20,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite 0.6s'
                            }} />
                          </Box>
                          <Typography variant="caption" sx={{
                            fontWeight: 600,
                            fontSize: '0.85rem'
                          }}>
                            Excellent Quality
                          </Typography>
                        </Box>
                      </Box>
                    )}

                    {/* Messages Area */}
                    {loading ? (
                      <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <CircularProgress />
                      </Box>
                    ) : (
                      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2, backgroundColor: '#f5f5f5' }}>
                        <List>
                          {messages.map((message) => (
                            <ListItem
                              key={message.id}
                              sx={{
                                textAlign: message.sender === 'user' ? 'right' : 'left',
                                mb: 2,
                                px: 2
                              }}
                            >
                              <Grid container spacing={2} direction={message.sender === 'user' ? 'row-reverse' : 'row'}>
                                <Grid item xs={1}>
                                  <Avatar
                                    sx={{
                                      bgcolor: message.sender === 'user' ? 'primary.main' : 'secondary.main',
                                    }}
                                  >
                                    {message.sender === 'user' ? currentUser?.email?.charAt(0).toUpperCase() || 'U' : selectedDoctor.name.charAt(0)}
                                  </Avatar>
                                </Grid>
                                <Grid item xs={11} sm={8} md={7}>
                                  <Paper
                                    elevation={1}
                                    sx={{
                                      p: 2,
                                      bgcolor: message.sender === 'user' ? 'primary.light' : 'white',
                                      color: message.sender === 'user' ? 'white' : 'text.primary',
                                      borderRadius: message.sender === 'user'
                                        ? '20px 20px 5px 20px'
                                        : '20px 20px 20px 5px',
                                      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                                    }}
                                  >
                                    <Typography variant="body1">{message.text}</Typography>
                                    <Typography variant="caption" sx={{ display: 'block', mt: 1, textAlign: 'right', opacity: 0.8 }}>
                                      {message.time}
                                    </Typography>
                                  </Paper>
                                </Grid>
                              </Grid>
                            </ListItem>
                          ))}
                          <div ref={messagesEndRef} />
                        </List>
                      </Box>
                    )}
                    
                    {/* Message Input */}
                    <Box sx={{ p: 2, backgroundColor: 'background.paper', borderTop: '1px solid', borderColor: 'divider' }}>
                      <form onSubmit={handleSendMessage}>
                        <Grid container spacing={1}>
                          <Grid item xs={11}>
                            <TextField
                              fullWidth
                              placeholder="Type a message"
                              variant="outlined"
                              value={newMessage}
                              onChange={(e) => setNewMessage(e.target.value)}
                              size="small"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: 2
                                }
                              }}
                            />
                          </Grid>
                          <Grid item xs={1}>
                            <Button 
                              variant="contained" 
                              color="primary"
                              type="submit"
                              disabled={!newMessage.trim()}
                              sx={{ minWidth: 0, p: 1, borderRadius: 2, height: '100%' }}
                            >
                              <SendIcon />
                            </Button>
                          </Grid>
                        </Grid>
                      </form>
                    </Box>
                  </>
                ) : (
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    justifyContent: 'center', 
                    alignItems: 'center', 
                    height: '100%',
                    p: 3,
                    textAlign: 'center'
                  }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Welcome to the Chat Portal
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      Select a healthcare provider from the list to start a conversation
                    </Typography>
                    <img 
                      src="/chat-illustration.svg" 
                      alt="Chat illustration" 
                      style={{ maxWidth: '60%', opacity: 0.7 }}
                    />
                  </Box>
                )}
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Layout>
  );
};

export default ChatPage;





























