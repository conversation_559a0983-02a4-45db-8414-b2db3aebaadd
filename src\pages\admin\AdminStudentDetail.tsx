import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid,
  Divider,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EmailIcon from '@mui/icons-material/Email';
import SchoolIcon from '@mui/icons-material/School';
import EventIcon from '@mui/icons-material/Event';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import HistoryIcon from '@mui/icons-material/History';
import NoteIcon from '@mui/icons-material/Note';

const AdminStudentDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [student, setStudent] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  
  // Mock appointments data
  const appointments = [
    { id: 1, doctor: 'Dr. Sarah Johnson', date: '2023-05-15', time: '10:00 AM', status: 'completed', type: 'Regular checkup' },
    { id: 2, doctor: 'Dr. <PERSON> Chen', date: '2023-06-20', time: '11:30 AM', status: 'scheduled', type: 'Flu symptoms' },
    { id: 3, doctor: 'Dr. Sarah Johnson', date: '2023-04-10', time: '09:15 AM', status: 'completed', type: 'Mental health consultation' },
  ];
  
  // Mock medical records data
  const medicalRecords = [
    { id: 1, date: '2023-05-15', doctor: 'Dr. Sarah Johnson', diagnosis: 'Common cold', treatment: 'Rest and fluids', notes: 'Patient should recover within a week.' },
    { id: 2, date: '2023-04-10', doctor: 'Dr. Sarah Johnson', diagnosis: 'Anxiety', treatment: 'Counseling sessions', notes: 'Recommended weekly sessions for the next month.' },
  ];
  
  useEffect(() => {
    // In a real app, you would fetch the student data from an API
    setLoading(true);
    setTimeout(() => {
      // Mock student data
      const mockStudent = {
        id: parseInt(id),
        name: 'John Smith',
        email: '<EMAIL>',
        department: 'Computer Science',
        status: 'active',
        joinDate: '2023-01-15',
        avatar: 'JS',
        allergies: ['Penicillin', 'Peanuts'],
        bloodType: 'O+',
        emergencyContact: 'Mary Smith (Mother) - (555) 123-4567'
      };
      
      setStudent(mockStudent);
      setLoading(false);
    }, 500);
  }, [id]);
  
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography>Loading student details...</Typography>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Student Details
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/admin/students')}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              Back to Students
            </Button>
            <Button 
              variant="contained" 
              startIcon={<EditIcon />}
              onClick={() => navigate(`/admin/students/${id}/edit`)}
              sx={{ borderRadius: 2 }}
            >
              Edit Student
            </Button>
          </Box>
        </Box>
        
        {/* Student Profile */}
        <Paper 
          sx={{ 
            p: 4, 
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={4}>
            {/* Avatar and Basic Info */}
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar 
                sx={{ 
                  width: 150, 
                  height: 150, 
                  fontSize: '3rem',
                  mb: 2,
                  bgcolor: 'secondary.main'
                }}
              >
                {generateAvatar(student.name)}
              </Avatar>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {student.name}
              </Typography>
              <Chip 
                label={student.status === 'active' ? 'Active' : 'Inactive'} 
                color={student.status === 'active' ? 'success' : 'default'}
                variant="outlined"
                sx={{ mb: 1 }}
              />
              <Chip 
                label={student.department} 
                color="secondary"
                sx={{ mb: 3 }}
              />
            </Grid>
            
            {/* Contact and Details */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Student Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EmailIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Email Address" 
                    secondary={student.email}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <SchoolIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Department" 
                    secondary={student.department}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Join Date" 
                    secondary={student.joinDate}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
              
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 3 }}>
                Medical Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <MedicalServicesIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Blood Type" 
                    secondary={student.bloodType}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters alignItems="flex-start">
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <NoteIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Allergies" 
                    secondary={
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                        {student.allergies.map((allergy, index) => (
                          <Chip 
                            key={index} 
                            label={allergy} 
                            size="small" 
                            color="error" 
                            variant="outlined" 
                          />
                        ))}
                      </Box>
                    }
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <HistoryIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Emergency Contact" 
                    secondary={student.emergencyContact}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Tabs for Appointments and Medical Records */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange}
              aria-label="student details tabs"
              sx={{ px: 2 }}
            >
              <Tab label="Appointments" />
              <Tab label="Medical Records" />
            </Tabs>
          </Box>
          
          {/* Appointments Tab */}
          <Box role="tabpanel" hidden={tabValue !== 0} sx={{ p: 3 }}>
            {tabValue === 0 && (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Appointment History
                  </Typography>
                  <Button 
                    variant="contained" 
                    color="primary"
                    size="small"
                    onClick={() => navigate('/admin/appointments/new')}
                    sx={{ borderRadius: 2 }}
                  >
                    Schedule New Appointment
                  </Button>
                </Box>
                
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                        <TableCell>Date</TableCell>
                        <TableCell>Time</TableCell>
                        <TableCell>Doctor</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {appointments.map((appointment) => (
                        <TableRow key={appointment.id}>
                          <TableCell>{appointment.date}</TableCell>
                          <TableCell>{appointment.time}</TableCell>
                          <TableCell>{appointment.doctor}</TableCell>
                          <TableCell>{appointment.type}</TableCell>
                          <TableCell>
                            <Chip 
                              label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)} 
                              color={
                                appointment.status === 'completed' ? 'success' : 
                                appointment.status === 'scheduled' ? 'primary' : 
                                'default'
                              }
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Button 
                              size="small" 
                              onClick={() => navigate(`/admin/appointments/${appointment.id}`)}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {appointments.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                              No appointments found for this student
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
          </Box>
          
          {/* Medical Records Tab */}
          <Box role="tabpanel" hidden={tabValue !== 1} sx={{ p: 3 }}>
            {tabValue === 1 && (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Medical Records
                  </Typography>
                  <Button 
                    variant="contained" 
                    color="primary"
                    size="small"
                    onClick={() => navigate('/admin/medical-records/new')}
                    sx={{ borderRadius: 2 }}
                  >
                    Add Medical Record
                  </Button>
                </Box>
                
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                        <TableCell>Date</TableCell>
                        <TableCell>Doctor</TableCell>
                        <TableCell>Diagnosis</TableCell>
                        <TableCell>Treatment</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {medicalRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>{record.date}</TableCell>
                          <TableCell>{record.doctor}</TableCell>
                          <TableCell>{record.diagnosis}</TableCell>
                          <TableCell>{record.treatment}</TableCell>
                          <TableCell align="right">
                            <Button 
                              size="small" 
                              onClick={() => navigate(`/admin/medical-records/${record.id}`)}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {medicalRecords.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                              No medical records found for this student
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminStudentDetail;