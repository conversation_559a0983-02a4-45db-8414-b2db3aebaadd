import React from 'react';
import {
  Container,
  Box,
  Typography,
  Button,
  Paper
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import BlockIcon from '@mui/icons-material/Block';
import Layout from '../../components/layout/Layout';

const Unauthorized: React.FC = () => {
  const navigate = useNavigate();
  const { userProfile } = useAuth();

  const handleGoBack = () => {
    // Redirect based on user role
    if (userProfile) {
      switch (userProfile.role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'doctor':
          navigate('/doctor/dashboard');
          break;
        case 'student':
          navigate('/dashboard');
          break;
        default:
          navigate('/');
      }
    } else {
      navigate('/');
    }
  };

  return (
    <Layout>
      <Container maxWidth="sm">
        <Box sx={{
          mt: { xs: 4, md: 8 },
          mb: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 3, md: 5 },
              borderRadius: 3,
              width: '100%',
              boxShadow: '0 8px 40px rgba(0,0,0,0.12)',
              background: 'linear-gradient(to bottom, #ffffff, #f9fafc)',
              textAlign: 'center'
            }}
          >
          <BlockIcon 
            sx={{ 
              fontSize: 80, 
              color: 'error.main',
              mb: 2
            }} 
          />
          
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Access Denied
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph sx={{ mb: 4 }}>
            You don't have permission to access this page. 
            Please contact an administrator if you believe this is an error.
          </Typography>
          
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleGoBack}
            sx={{
              py: 1.5,
              px: 4,
              borderRadius: 2,
              fontWeight: 'bold',
              boxShadow: '0 4px 12px rgba(0,114,255,0.3)'
            }}
          >
            Go Back
          </Button>
        </Paper>
      </Box>
    </Container>
    </Layout>
  );
};

export default Unauthorized;
