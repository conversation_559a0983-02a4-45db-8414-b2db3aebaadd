import React from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Button, 
  Paper 
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import BlockIcon from '@mui/icons-material/Block';

const Unauthorized: React.FC = () => {
  const navigate = useNavigate();
  const { userProfile } = useAuth();

  const handleGoBack = () => {
    // Redirect based on user role
    if (userProfile) {
      switch (userProfile.role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'doctor':
          navigate('/doctor/dashboard');
          break;
        case 'student':
          navigate('/dashboard');
          break;
        default:
          navigate('/');
      }
    } else {
      navigate('/');
    }
  };

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          py: 4
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            width: '100%',
            borderRadius: 3,
            textAlign: 'center'
          }}
        >
          <BlockIcon 
            sx={{ 
              fontSize: 80, 
              color: 'error.main',
              mb: 2
            }} 
          />
          
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Access Denied
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph sx={{ mb: 4 }}>
            You don't have permission to access this page. 
            Please contact an administrator if you believe this is an error.
          </Typography>
          
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleGoBack}
            sx={{ 
              py: 1.5, 
              px: 4,
              borderRadius: 2
            }}
          >
            Go Back
          </Button>
        </Paper>
      </Box>
    </Container>
  );
};

export default Unauthorized;
