import React from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Paper, 
  Button,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useFirebase } from '../../contexts/FirebaseContext';
import Layout from '../../components/layout/Layout';
import DashboardIcon from '@mui/icons-material/Dashboard';
import MedicationIcon from '@mui/icons-material/Medication';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import ChatIcon from '@mui/icons-material/Chat';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import ArticleIcon from '@mui/icons-material/Article';
import PersonIcon from '@mui/icons-material/Person';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';

const Dashboard = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();

  // Mock data for upcoming appointments
  const upcomingAppointments = [
    { id: 1, doctor: 'Dr. <PERSON>', specialty: 'General Physician', date: 'May 15, 2023', time: '10:00 AM' },
    { id: 2, doctor: 'Dr. Michael Chen', specialty: 'Dermatologist', date: 'May 22, 2023', time: '2:30 PM' }
  ];

  // Mock data for recent health tips
  const recentHealthTips = [
    { id: 1, title: 'Managing Exam Stress', category: 'Mental Health', date: 'May 5, 2023' },
    { id: 2, title: 'Nutrition Tips for Students', category: 'Nutrition', date: 'May 3, 2023' },
    { id: 3, title: 'Importance of Sleep', category: 'Wellness', date: 'April 28, 2023' }
  ];

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Dashboard Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Welcome back, {currentUser?.displayName || 'User'}
            </Typography>
          </Box>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<HealthAndSafetyIcon />}
            onClick={() => navigate('/symptom-checker')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            Check Symptoms
          </Button>
        </Box>

        <Grid container spacing={3}>
          {/* Quick Actions */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Quick Actions
              </Typography>
              <Divider sx={{ my: 2 }} />
              <List sx={{ py: 0 }}>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/profile')}
                >
                  <ListItemIcon>
                    <PersonIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="View Profile" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/appointments')}
                >
                  <ListItemIcon>
                    <CalendarMonthIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Book Appointment" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/chat')}
                >
                  <ListItemIcon>
                    <ChatIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Messages" />
                </ListItem>
                <ListItem 
                  button 
                  sx={{ 
                    borderRadius: 2,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/health-resources')}
                >
                  <ListItemIcon>
                    <ArticleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Health Resources" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Upcoming Appointments */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Upcoming Appointments
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/appointments')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              {upcomingAppointments.length > 0 ? (
                <Grid container spacing={2}>
                  {upcomingAppointments.map((appointment) => (
                    <Grid item xs={12} sm={6} key={appointment.id}>
                      <Card 
                        sx={{ 
                          borderRadius: 2,
                          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column'
                        }}
                      >
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                              {appointment.doctor.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {appointment.doctor}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {appointment.specialty}
                              </Typography>
                            </Box>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <CalendarMonthIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1 }} />
                            <Typography variant="body2">
                              {appointment.date} at {appointment.time}
                            </Typography>
                          </Box>
                        </CardContent>
                        <CardActions sx={{ p: 2, pt: 0 }}>
                          <Button 
                            size="small" 
                            variant="outlined" 
                            fullWidth
                            sx={{ borderRadius: 2 }}
                          >
                            Reschedule
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CalendarMonthIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No upcoming appointments
                  </Typography>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    sx={{ mt: 2, borderRadius: 2 }}
                    onClick={() => navigate('/appointments/new')}
                  >
                    Book Appointment
                  </Button>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Health Tips */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Recent Health Tips
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/health-resources')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <List>
                {recentHealthTips.map((tip) => (
                  <ListItem 
                    key={tip.id}
                    sx={{ 
                      px: 2, 
                      py: 1.5, 
                      borderRadius: 2,
                      mb: 1,
                      '&:hover': { 
                        bgcolor: 'rgba(0,114,255,0.08)',
                        cursor: 'pointer'
                      }
                    }}
                    onClick={() => navigate(`/health-resources/${tip.id}`)}
                  >
                    <ListItemIcon>
                      <ArticleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={tip.title}
                      secondary={`${tip.category} • ${tip.date}`}
                      primaryTypographyProps={{ fontWeight: 'medium' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Notifications */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NotificationsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Notifications
                </Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <List sx={{ py: 0 }}>
                <ListItem 
                  sx={{ 
                    px: 2, 
                    py: 1.5, 
                    borderRadius: 2,
                    mb: 1,
                    bgcolor: 'rgba(0,114,255,0.08)'
                  }}
                >
                  <ListItemText 
                    primary="Appointment Reminder"
                    secondary="You have an appointment tomorrow at 10:00 AM"
                    primaryTypographyProps={{ fontWeight: 'medium' }}
                  />
                  <Chip 
                    label="New" 
                    size="small" 
                    color="primary" 
                    sx={{ ml: 1 }} 
                  />
                </ListItem>
                <ListItem 
                  sx={{ 
                    px: 2, 
                    py: 1.5, 
                    borderRadius: 2,
                    mb: 1
                  }}
                >
                  <ListItemText 
                    primary="New Health Tip"
                    secondary="Check out our latest article on stress management"
                    primaryTypographyProps={{ fontWeight: 'medium' }}
                  />
                </ListItem>
                <ListItem 
                  sx={{ 
                    px: 2, 
                    py: 1.5, 
                    borderRadius: 2
                  }}
                >
                  <ListItemText 
                    primary="Message from Dr. Johnson"
                    secondary="Your test results are ready for review"
                    primaryTypographyProps={{ fontWeight: 'medium' }}
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Key Health Features */}
          <Grid item xs={12}>
            <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mt: 2 }}>
              Key Health Features
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/medications')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <MedicationIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Medications
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Track your medications, set reminders, and manage refills
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/health-metrics')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <MonitorHeartIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Health Metrics
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Monitor your vital signs and track health trends over time
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/medical-records')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <MedicalInformationIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Medical Records
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Access and manage your medical documents securely
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/chat')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <ChatIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Chat
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Message your healthcare providers securely
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default Dashboard;





