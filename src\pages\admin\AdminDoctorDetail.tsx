import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid,
  Divider,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Rating
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EmailIcon from '@mui/icons-material/Email';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import EventIcon from '@mui/icons-material/Event';
import PhoneIcon from '@mui/icons-material/Phone';
import StarIcon from '@mui/icons-material/Star';
import ScheduleIcon from '@mui/icons-material/Schedule';
import SchoolIcon from '@mui/icons-material/School';
import WorkIcon from '@mui/icons-material/Work';
import LanguageIcon from '@mui/icons-material/Language';

const AdminDoctorDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [doctor, setDoctor] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  
  // Mock appointments data
  const appointments = [
    { id: 1, student: 'John Smith', date: '2023-05-15', time: '10:00 AM', status: 'completed', type: 'Regular checkup' },
    { id: 2, student: 'Emma Davis', date: '2023-06-20', time: '11:30 AM', status: 'scheduled', type: 'Flu symptoms' },
    { id: 3, student: 'Alex Rodriguez', date: '2023-04-10', time: '09:15 AM', status: 'completed', type: 'Mental health consultation' },
    { id: 4, student: 'Sophia Martinez', date: '2023-06-25', time: '02:00 PM', status: 'scheduled', type: 'Follow-up' },
  ];
  
  // Mock reviews data
  const reviews = [
    { id: 1, student: 'John Smith', date: '2023-05-16', rating: 5, comment: 'Dr. Johnson was very professional and caring. Highly recommend!' },
    { id: 2, student: 'Emma Davis', date: '2023-04-12', rating: 4, comment: 'Good experience overall. The doctor was knowledgeable and helpful.' },
    { id: 3, student: 'Alex Rodriguez', date: '2023-03-22', rating: 5, comment: 'Excellent care and attention. Made me feel comfortable during the consultation.' },
  ];
  
  useEffect(() => {
    // In a real app, you would fetch the doctor data from an API
    setLoading(true);
    setTimeout(() => {
      // Mock doctor data
      const mockDoctor = {
        id: parseInt(id),
        name: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        specialty: 'Cardiology',
        status: 'active',
        joinDate: '2023-01-10',
        phone: '(*************',
        avatar: 'SJ',
        rating: 4.7,
        education: 'MD, Harvard Medical School',
        experience: '10+ years',
        availability: 'Mon-Fri, 9:00 AM - 5:00 PM',
        languages: ['English', 'Spanish'],
        totalAppointments: 145
      };
      
      setDoctor(mockDoctor);
      setLoading(false);
    }, 500);
  }, [id]);
  
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography>Loading doctor details...</Typography>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Doctor Details
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/admin/doctors')}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              Back to Doctors
            </Button>
            <Button 
              variant="contained" 
              startIcon={<EditIcon />}
              onClick={() => navigate(`/admin/doctors/${id}/edit`)}
              sx={{ borderRadius: 2 }}
            >
              Edit Doctor
            </Button>
          </Box>
        </Box>
        
        {/* Doctor Profile */}
        <Paper 
          sx={{ 
            p: 4, 
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={4}>
            {/* Avatar and Basic Info */}
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar 
                sx={{ 
                  width: 150, 
                  height: 150, 
                  fontSize: '3rem',
                  mb: 2,
                  bgcolor: 'primary.main'
                }}
              >
                {generateAvatar(doctor.name)}
              </Avatar>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {doctor.name}
              </Typography>
              <Chip 
                label={doctor.status === 'active' ? 'Active' : 'Inactive'} 
                color={doctor.status === 'active' ? 'success' : 'default'}
                variant="outlined"
                sx={{ mb: 1 }}
              />
              <Chip 
                label={doctor.specialty} 
                color="primary"
                sx={{ mb: 2 }}
              />
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Rating 
                  value={doctor.rating} 
                  precision={0.1} 
                  readOnly 
                  size="small"
                  emptyIcon={<StarIcon style={{ opacity: 0.55 }} fontSize="inherit" />}
                />
                <Typography variant="body2" sx={{ ml: 1 }}>
                  {doctor.rating} / 5.0
                </Typography>
              </Box>
              
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Total Appointments
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {doctor.totalAppointments}
                </Typography>
              </Box>
            </Grid>
            
            {/* Contact and Details */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Contact Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EmailIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Email Address" 
                    secondary={doctor.email}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <PhoneIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Phone Number" 
                    secondary={doctor.phone}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Join Date" 
                    secondary={doctor.joinDate}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
              
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 3 }}>
                Professional Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <LocalHospitalIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Specialty" 
                    secondary={doctor.specialty}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <SchoolIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Education" 
                    secondary={doctor.education}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <WorkIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Experience" 
                    secondary={doctor.experience}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <ScheduleIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Availability" 
                    secondary={doctor.availability}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters alignItems="flex-start">
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <LanguageIcon color="action" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Languages" 
                    secondary={
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                        {doctor.languages.map((language, index) => (
                          <Chip 
                            key={index} 
                            label={language} 
                            size="small" 
                            color="info" 
                            variant="outlined" 
                          />
                        ))}
                      </Box>
                    }
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Tabs for Appointments and Reviews */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange}
              aria-label="doctor details tabs"
              sx={{ px: 2 }}
            >
              <Tab label="Appointments" />
              <Tab label="Reviews" />
            </Tabs>
          </Box>
          
          {/* Appointments Tab */}
          <Box role="tabpanel" hidden={tabValue !== 0} sx={{ p: 3 }}>
            {tabValue === 0 && (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Appointment Schedule
                  </Typography>
                  <Button 
                    variant="contained" 
                    color="primary"
                    size="small"
                    onClick={() => navigate('/admin/appointments/new')}
                    sx={{ borderRadius: 2 }}
                  >
                    Schedule New Appointment
                  </Button>
                </Box>
                
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                        <TableCell>Date</TableCell>
                        <TableCell>Time</TableCell>
                        <TableCell>Student</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {appointments.map((appointment) => (
                        <TableRow key={appointment.id}>
                          <TableCell>{appointment.date}</TableCell>
                          <TableCell>{appointment.time}</TableCell>
                          <TableCell>{appointment.student}</TableCell>
                          <TableCell>{appointment.type}</TableCell>
                          <TableCell>
                            <Chip 
                              label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)} 
                              color={
                                appointment.status === 'completed' ? 'success' : 
                                appointment.status === 'scheduled' ? 'primary' : 
                                'default'
                              }
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Button 
                              size="small" 
                              onClick={() => navigate(`/admin/appointments/${appointment.id}`)}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {appointments.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                              No appointments found for this doctor
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
          </Box>
          
          {/* Reviews Tab */}
          <Box role="tabpanel" hidden={tabValue !== 1} sx={{ p: 3 }}>
            {tabValue === 1 && (
              <>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Patient Reviews
                  </Typography>
                </Box>
                
                {reviews.map((review) => (
                  <Paper 
                    key={review.id}
                    elevation={0}
                    sx={{ 
                      p: 2, 
                      mb: 2, 
                      border: '1px solid rgba(0,0,0,0.1)',
                      borderRadius: 2
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="subtitle1" fontWeight="medium">
                        {review.student}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {review.date}
                      </Typography>
                    </Box>
                    <Rating 
                      value={review.rating} 
                      readOnly 
                      size="small"
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="body2">
                      {review.comment}
                    </Typography>
                  </Paper>
                ))}
                
                {reviews.length === 0 && (
                  <Box sx={{ py: 3, textAlign: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                      No reviews found for this doctor
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminDoctorDetail;