import React, { useState } from 'react';
import { 
  Container, 
  Typo<PERSON>, 
  Box, 
  Paper, 
  Button,
  Grid,
  TextField,
  Divider,
  Switch,
  FormControlLabel,
  FormGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Snackbar,
  Alert
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';

// Icons
import SettingsIcon from '@mui/icons-material/Settings';
import SecurityIcon from '@mui/icons-material/Security';
import NotificationsIcon from '@mui/icons-material/Notifications';
import StorageIcon from '@mui/icons-material/Storage';
import LanguageIcon from '@mui/icons-material/Language';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SaveIcon from '@mui/icons-material/Save';
import RestoreIcon from '@mui/icons-material/Restore';

const AdminSettings = () => {
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('general');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  
  // General settings
  const [siteName, setSiteName] = useState('University Health Portal');
  const [siteDescription, setSiteDescription] = useState('Health management system for university students and staff');
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  
  // Security settings
  const [passwordPolicy, setPasswordPolicy] = useState('medium');
  const [sessionTimeout, setSessionTimeout] = useState(30);
  const [twoFactorAuth, setTwoFactorAuth] = useState(false);
  const [ipRestriction, setIpRestriction] = useState(false);
  
  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [appointmentReminders, setAppointmentReminders] = useState(true);
  const [reminderTime, setReminderTime] = useState(24);
  
  const handleSaveSettings = () => {
    // Here you would implement the actual save logic
    console.log('Saving settings...');
    setSnackbarOpen(true);
  };
  
  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  const renderSettingsContent = () => {
    switch (activeSection) {
      case 'general':
        return (
          <>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              General Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  label="Site Name"
                  value={siteName}
                  onChange={(e) => setSiteName(e.target.value)}
                  fullWidth
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Site Description"
                  value={siteDescription}
                  onChange={(e) => setSiteDescription(e.target.value)}
                  fullWidth
                  variant="outlined"
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel 
                    control={
                      <Switch 
                        checked={maintenanceMode} 
                        onChange={(e) => setMaintenanceMode(e.target.checked)} 
                      />
                    } 
                    label="Maintenance Mode" 
                  />
                  <Typography variant="caption" color="text.secondary">
                    When enabled, the site will display a maintenance message to all non-admin users.
                  </Typography>
                </FormGroup>
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel 
                    control={
                      <Switch 
                        checked={debugMode} 
                        onChange={(e) => setDebugMode(e.target.checked)} 
                      />
                    } 
                    label="Debug Mode" 
                  />
                  <Typography variant="caption" color="text.secondary">
                    When enabled, detailed error messages will be displayed. Not recommended for production.
                  </Typography>
                </FormGroup>
              </Grid>
            </Grid>
          </>
        );
      case 'security':
        return (
          <>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Security Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="password-policy-label">Password Policy</InputLabel>
                  <Select
                    labelId="password-policy-label"
                    id="password-policy"
                    value={passwordPolicy}
                    label="Password Policy"
                    onChange={(e) => setPasswordPolicy(e.target.value)}
                  >
                    <MenuItem value="low">Low (min 6 characters)</MenuItem>
                    <MenuItem value="medium">Medium (min 8 chars, 1 number)</MenuItem>
                    <MenuItem value="high">High (min 10 chars, mixed case, numbers, symbols)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Session Timeout (minutes)"
                  type="number"
                  value={sessionTimeout}
                  onChange={(e) => setSessionTimeout(parseInt(e.target.value))}
                  fullWidth
                  variant="outlined"
                  InputProps={{ inputProps: { min: 5, max: 120 } }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel 
                    control={
                      <Switch 
                        checked={twoFactorAuth} 
                        onChange={(e) => setTwoFactorAuth(e.target.checked)} 
                      />
                    } 
                    label="Two-Factor Authentication" 
                  />
                  <Typography variant="caption" color="text.secondary">
                    Require two-factor authentication for all admin users.
                  </Typography>
                </FormGroup>
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel 
                    control={
                      <Switch 
                        checked={ipRestriction} 
                        onChange={(e) => setIpRestriction(e.target.checked)} 
                      />
                    } 
                    label="IP Restriction" 
                  />
                  <Typography variant="caption" color="text.secondary">
                    Restrict admin access to specific IP addresses.
                  </Typography>
                </FormGroup>
              </Grid>
            </Grid>
          </>
        );
      case 'notifications':
        return (
          <>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Notification Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel 
                    control={
                      <Switch 
                        checked={emailNotifications} 
                        onChange={(e) => setEmailNotifications(e.target.checked)} 
                      />
                    } 
                    label="Email Notifications" 
                  />
                </FormGroup>
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel 
                    control={
                      <Switch 
                        checked={smsNotifications} 
                        onChange={(e) => setSmsNotifications(e.target.checked)} 
                      />
                    } 
                    label="SMS Notifications" 
                  />
                </FormGroup>
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel 
                    control={
                      <Switch 
                        checked={appointmentReminders} 
                        onChange={(e) => setAppointmentReminders(e.target.checked)} 
                      />
                    } 
                    label="Appointment Reminders" 
                  />
                </FormGroup>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Reminder Time (hours before appointment)"
                  type="number"
                  value={reminderTime}
                  onChange={(e) => setReminderTime(parseInt(e.target.value))}
                  fullWidth
                  variant="outlined"
                  disabled={!appointmentReminders}
                  InputProps={{ inputProps: { min: 1, max: 72 } }}
                />
              </Grid>
            </Grid>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            System Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure and manage system-wide settings
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Settings Navigation */}
          <Grid item xs={12} md={3}>
            <Paper 
              sx={{ 
                p: 0, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                overflow: 'hidden'
              }}
            >
              <List component="nav" sx={{ p: 0 }}>
                <ListItem 
                  button 
                  selected={activeSection === 'general'}
                  onClick={() => setActiveSection('general')}
                  sx={{ 
                    py: 2,
                    borderLeft: activeSection === 'general' ? 4 : 0,
                    borderColor: 'primary.main',
                    '&.Mui-selected': {
                      bgcolor: 'rgba(0,114,255,0.08)'
                    }
                  }}
                >
                  <ListItemIcon>
                    <SettingsIcon color={activeSection === 'general' ? 'primary' : 'inherit'} />
                  </ListItemIcon>
                  <ListItemText primary="General Settings" />
                </ListItem>
                <ListItem 
                  button 
                  selected={activeSection === 'security'}
                  onClick={() => setActiveSection('security')}
                  sx={{ 
                    py: 2,
                    borderLeft: activeSection === 'security' ? 4 : 0,
                    borderColor: 'primary.main',
                    '&.Mui-selected': {
                      bgcolor: 'rgba(0,114,255,0.08)'
                    }
                  }}
                >
                  <ListItemIcon>
                    <SecurityIcon color={activeSection === 'security' ? 'primary' : 'inherit'} />
                  </ListItemIcon>
                  <ListItemText primary="Security Settings" />
                </ListItem>
                <ListItem 
                  button 
                  selected={activeSection === 'notifications'}
                  onClick={() => setActiveSection('notifications')}
                  sx={{ 
                    py: 2,
                    borderLeft: activeSection === 'notifications' ? 4 : 0,
                    borderColor: 'primary.main',
                    '&.Mui-selected': {
                      bgcolor: 'rgba(0,114,255,0.08)'
                    }
                  }}
                >
                  <ListItemIcon>
                    <NotificationsIcon color={activeSection === 'notifications' ? 'primary' : 'inherit'} />
                  </ListItemIcon>
                  <ListItemText primary="Notification Settings" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Settings Content */}
          <Grid item xs={12} md={9}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              {renderSettingsContent()}
              
              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button 
                  variant="outlined" 
                  startIcon={<RestoreIcon />}
                  sx={{ borderRadius: 2 }}
                >
                  Reset to Default
                </Button>
                <Button 
                  variant="contained" 
                  color="primary"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveSettings}
                  sx={{ borderRadius: 2 }}
                >
                  Save Changes
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          Settings saved successfully!
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminSettings;