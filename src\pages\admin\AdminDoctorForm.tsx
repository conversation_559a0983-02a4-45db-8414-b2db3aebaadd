import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  Snackbar,
  Alert,
  Avatar
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';

const AdminDoctorForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditMode = Boolean(id);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    specialty: '',
    status: 'active',
    joinDate: '',
    phone: '',
    avatar: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  
  // Mock specialties data
  const specialties = ['General Medicine', 'Cardiology', 'Dermatology', 'Psychiatry', 'Orthopedics', 'Neurology'];
  
  useEffect(() => {
    if (isEditMode) {
      // In a real app, you would fetch the doctor data from an API
      setLoading(true);
      setTimeout(() => {
        // Mock doctor data
        const mockDoctor = {
          id: parseInt(id),
          name: 'Dr. Sarah Johnson',
          email: '<EMAIL>',
          specialty: 'Cardiology',
          status: 'active',
          joinDate: '2023-01-10',
          phone: '(*************',
          avatar: 'SJ'
        };
        
        setFormData({
          name: mockDoctor.name,
          email: mockDoctor.email,
          specialty: mockDoctor.specialty,
          status: mockDoctor.status,
          joinDate: mockDoctor.joinDate,
          phone: mockDoctor.phone,
          avatar: mockDoctor.avatar
        });
        
        setLoading(false);
      }, 500);
    }
  }, [id, isEditMode]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    
    // In a real app, you would send the data to an API
    setTimeout(() => {
      setLoading(false);
      setSnackbarMessage(isEditMode ? 'Doctor updated successfully!' : 'Doctor created successfully!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      
      // Navigate back to doctors list after a short delay
      setTimeout(() => {
        navigate('/admin/doctors');
      }, 1500);
    }, 1000);
  };
  
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  
  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            {isEditMode ? 'Edit Doctor' : 'Add New Doctor'}
          </Typography>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/admin/doctors')}
            sx={{ borderRadius: 2 }}
          >
            Back to Doctors
          </Button>
        </Box>
        
        {/* Form */}
        <Paper 
          component="form"
          onSubmit={handleSubmit}
          sx={{ 
            p: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={3}>
            {/* Avatar Preview */}
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <Avatar 
                sx={{ 
                  width: 100, 
                  height: 100, 
                  fontSize: '2rem',
                  bgcolor: 'primary.main'
                }}
              >
                {generateAvatar(formData.name)}
              </Avatar>
            </Grid>
            
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Full Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Email Address"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Phone Number"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Specialty</InputLabel>
                <Select
                  name="specialty"
                  value={formData.specialty}
                  onChange={handleChange}
                  label="Specialty"
                  disabled={loading}
                >
                  {specialties.map((specialty) => (
                    <MenuItem key={specialty} value={specialty}>{specialty}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label="Status"
                  disabled={loading}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Join Date"
                name="joinDate"
                type="date"
                value={formData.joinDate}
                onChange={handleChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            
            {/* Submit Button */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                startIcon={<SaveIcon />}
                disabled={loading}
                sx={{ 
                  borderRadius: 2,
                  px: 4,
                  py: 1.5
                }}
              >
                {loading ? 'Saving...' : isEditMode ? 'Update Doctor' : 'Create Doctor'}
              </Button>
            </Grid>
          </Grid>
        </Paper>
      </Container>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity} 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminDoctorForm;