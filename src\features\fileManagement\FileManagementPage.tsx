import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Grid, Paper, Button, 
  List, ListItem, ListItemText, ListItemIcon, Divider,
  IconButton, TextField, Dialog, DialogTitle, DialogContent,
  DialogActions, CircularProgress, Chip, Alert, LinearProgress
} from '@mui/material';
import { 
  CloudUpload as UploadIcon, 
  Description as FileIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  InsertDriveFile as GenericFileIcon,
  Folder as FolderIcon
} from '@mui/icons-material';
import { useFirebase } from '../../contexts/FirebaseContext';
import Layout from '../../components/layout/Layout';
import { useFileUpload } from '../../hooks/useFileUpload';
import { deleteFile, FileCategory } from '../../services/storageService';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  getDocs, 
  doc, 
  setDoc, 
  deleteDoc, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../../services/firebase';
import { getDownloadURL, ref } from 'firebase/storage';
import { storage } from '../../services/firebase';

interface FileItem {
  id: string;
  name: string;
  type: string;
  size: string;
  date: string;
  path?: string;
  url?: string;
}

interface FolderItem {
  id: string;
  name: string;
  date: string;
}

const FileManagementPage = () => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [currentFolder, setCurrentFolder] = useState<FolderItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [newFolderDialogOpen, setNewFolderDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileCategory, setFileCategory] = useState<FileCategory>('medical_records');
  const { currentUser } = useFirebase();
  const { uploadState, uploadFile, resetUploadState } = useFileUpload();

  // Load files and folders
  useEffect(() => {
    if (!currentUser) return;
    
    const loadFilesAndFolders = async () => {
      setLoading(true);
      
      try {
        // Get folders
        const foldersRef = collection(db, 'users', currentUser.uid, 'folders');
        const foldersQuery = query(foldersRef, orderBy('createdAt', 'desc'));
        const folderSnapshot = await getDocs(foldersQuery);
        
        const folderData: FolderItem[] = folderSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            name: data.name,
            date: data.createdAt?.toDate?.()?.toISOString?.().split('T')[0] || new Date().toISOString().split('T')[0]
          };
        });
        
        setFolders(folderData);
        
        // Get root files (not in any folder)
        const filesRef = collection(db, 'users', currentUser.uid, 'files');
        const filesQuery = query(
          filesRef, 
          where('folderId', '==', null),
          orderBy('createdAt', 'desc')
        );
        const fileSnapshot = await getDocs(filesQuery);
        
        const fileData: FileItem[] = fileSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            name: data.name,
            type: data.type,
            size: formatFileSize(data.size),
            date: data.createdAt?.toDate?.()?.toISOString?.().split('T')[0] || new Date().toISOString().split('T')[0],
            path: data.path,
            url: data.url
          };
        });
        
        setFiles(fileData);
      } catch (error) {
        console.error('Error loading files and folders:', error);
        // If there's an error, use mock data
        const mockFolders = [
          { id: 'f1', name: 'Lab Results', date: '2023-05-15' },
          { id: 'f2', name: 'Prescriptions', date: '2023-04-20' },
          { id: 'f3', name: 'Imaging', date: '2023-03-10' }
        ];
        
        const mockFiles = [
          { id: '1', name: 'Blood Test Results.pdf', type: 'pdf', size: '1.2 MB', date: '2023-06-01' },
          { id: '2', name: 'Chest X-Ray.jpg', type: 'image', size: '3.5 MB', date: '2023-05-20' },
          { id: '3', name: 'Prescription.pdf', type: 'pdf', size: '0.8 MB', date: '2023-05-15' },
          { id: '4', name: 'Medical History.docx', type: 'document', size: '1.5 MB', date: '2023-04-10' }
        ];
        
        setFolders(mockFolders);
        setFiles(mockFiles);
      } finally {
        setLoading(false);
      }
    };
    
    loadFilesAndFolders();
  }, [currentUser]);

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const handleFolderClick = async (folder: FolderItem) => {
    if (!currentUser) return;
    
    setCurrentFolder(folder);
    setLoading(true);
    
    try {
      // Get files in this folder
      const filesRef = collection(db, 'users', currentUser.uid, 'files');
      const filesQuery = query(
        filesRef, 
        where('folderId', '==', folder.id),
        orderBy('createdAt', 'desc')
      );
      const fileSnapshot = await getDocs(filesQuery);
      
      const fileData: FileItem[] = fileSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          name: data.name,
          type: data.type,
          size: formatFileSize(data.size),
          date: data.createdAt?.toDate?.()?.toISOString?.().split('T')[0] || new Date().toISOString().split('T')[0],
          path: data.path,
          url: data.url
        };
      });
      
      setFiles(fileData);
    } catch (error) {
      console.error('Error loading folder files:', error);
      // If there's an error, use mock data based on folder
      let folderFiles: FileItem[] = [];
      
      if (folder.id === 'f1') {
        folderFiles = [
          { id: '5', name: 'Blood Test - January.pdf', type: 'pdf', size: '1.1 MB', date: '2023-01-15' },
          { id: '6', name: 'Blood Test - April.pdf', type: 'pdf', size: '1.2 MB', date: '2023-04-20' },
          { id: '7', name: 'Cholesterol Results.pdf', type: 'pdf', size: '0.9 MB', date: '2023-05-10' }
        ];
      } else if (folder.id === 'f2') {
        folderFiles = [
          { id: '8', name: 'Amoxicillin.pdf', type: 'pdf', size: '0.7 MB', date: '2023-03-05' },
          { id: '9', name: 'Lisinopril.pdf', type: 'pdf', size: '0.8 MB', date: '2023-04-15' }
        ];
      } else if (folder.id === 'f3') {
        folderFiles = [
          { id: '10', name: 'MRI Scan.jpg', type: 'image', size: '5.2 MB', date: '2023-02-20' },
          { id: '11', name: 'X-Ray Left Arm.jpg', type: 'image', size: '3.1 MB', date: '2023-03-10' },
          { id: '12', name: 'CT Scan Report.pdf', type: 'pdf', size: '1.5 MB', date: '2023-03-15' }
        ];
      }
      
      setFiles(folderFiles);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToRoot = async () => {
    if (!currentUser) return;
    
    setCurrentFolder(null);
    setLoading(true);
    
    try {
      // Get root files (not in any folder)
      const filesRef = collection(db, 'users', currentUser.uid, 'files');
      const filesQuery = query(
        filesRef, 
        where('folderId', '==', null),
        orderBy('createdAt', 'desc')
      );
      const fileSnapshot = await getDocs(filesQuery);
      
      const fileData: FileItem[] = fileSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          name: data.name,
          type: data.type,
          size: formatFileSize(data.size),
          date: data.createdAt?.toDate?.()?.toISOString?.().split('T')[0] || new Date().toISOString().split('T')[0],
          path: data.path,
          url: data.url
        };
      });
      
      setFiles(fileData);
    } catch (error) {
      console.error('Error loading root files:', error);
      // If there's an error, use mock data
      const mockFiles = [
        { id: '1', name: 'Blood Test Results.pdf', type: 'pdf', size: '1.2 MB', date: '2023-06-01' },
        { id: '2', name: 'Chest X-Ray.jpg', type: 'image', size: '3.5 MB', date: '2023-05-20' },
        { id: '3', name: 'Prescription.pdf', type: 'pdf', size: '0.8 MB', date: '2023-05-15' },
        { id: '4', name: 'Medical History.docx', type: 'document', size: '1.5 MB', date: '2023-04-10' }
      ];
      
      setFiles(mockFiles);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenUploadDialog = () => {
    setUploadDialogOpen(true);
    resetUploadState();
  };

  const handleCloseUploadDialog = () => {
    setUploadDialogOpen(false);
    setSelectedFile(null);
    resetUploadState();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleUploadFile = async () => {
    if (!selectedFile || !currentUser) return;
    
    try {
      // Determine file type category
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase() || '';
      let fileType = 'document';
      
      if (['pdf'].includes(fileExtension)) {
        fileType = 'pdf';
      } else if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
        fileType = 'image';
      }
      
      // Upload file to Firebase Storage
      await uploadFile(selectedFile, fileCategory, {
        description: 'Uploaded from file management',
        tags: [fileCategory, fileType]
      });
      
      // Add file metadata to Firestore
      if (uploadState.fileUrl && uploadState.fileId) {
        const filesRef = collection(db, 'users', currentUser.uid, 'files');
        await setDoc(doc(filesRef, uploadState.fileId), {
          name: selectedFile.name,
          type: fileType,
          size: selectedFile.size,
          url: uploadState.fileUrl,
          category: fileCategory,
          folderId: currentFolder?.id || null,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        
        // Add the new file to the UI
        const newFile: FileItem = {
          id: uploadState.fileId,
          name: selectedFile.name,
          type: fileType,
          size: formatFileSize(selectedFile.size),
          date: new Date().toISOString().split('T')[0],
          url: uploadState.fileUrl
        };
        
        setFiles([newFile, ...files]);
      }
      
      handleCloseUploadDialog();
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  };

  const handleOpenNewFolderDialog = () => {
    setNewFolderDialogOpen(true);
  };

  const handleCloseNewFolderDialog = () => {
    setNewFolderDialogOpen(false);
    setNewFolderName('');
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim() || !currentUser) return;
    
    setLoading(true);
    setNewFolderDialogOpen(false);
    
    try {
      // Add folder to Firestore
      const foldersRef = collection(db, 'users', currentUser.uid, 'folders');
      const folderData = {
        name: newFolderName,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };
      
      const docRef = doc(foldersRef);
      await setDoc(docRef, folderData);
      
      // Add the new folder to the UI
      const newFolder: FolderItem = {
        id: docRef.id,
        name: newFolderName,
        date: new Date().toISOString().split('T')[0]
      };
      
      setFolders([newFolder, ...folders]);
    } catch (error) {
      console.error('Error creating folder:', error);
      // If there's an error, still update the UI with mock data
      const newFolder: FolderItem = {
        id: `folder-${Date.now()}`,
        name: newFolderName,
        date: new Date().toISOString().split('T')[0]
      };
      
      setFolders([newFolder, ...folders]);
    } finally {
      setLoading(false);
      setNewFolderName('');
    }
  };

  const handleDeleteFile = async (fileId: string, filePath?: string) => {
    if (!currentUser || !fileId) return;
    
    setLoading(true);
    
    try {
      // Delete from Firestore
      await deleteDoc(doc(db, 'users', currentUser.uid, 'files', fileId));
      
      // Delete from Storage if path exists
      if (filePath) {
        await deleteFile(currentUser.uid, fileId, 'medical_records', filePath);
      }
      
      // Update UI
      setFiles(files.filter(file => file.id !== fileId));
    } catch (error) {
      console.error('Error deleting file:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadFile = async (fileUrl?: string, fileName?: string) => {
    if (!fileUrl || !fileName) return;
    
    try {
      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'pdf':
        return <PdfIcon color="error" />;
      case 'image':
        return <ImageIcon color="primary" />;
      default:
        return <GenericFileIcon color="action" />;
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            {currentFolder ? currentFolder.name : 'Medical Records'}
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<AddIcon />}
              onClick={handleOpenNewFolderDialog}
              sx={{ mr: 1 }}
            >
              New Folder
            </Button>
            <Button 
              variant="contained" 
              startIcon={<UploadIcon />}
              onClick={handleOpenUploadDialog}
            >
              Upload File
            </Button>
          </Box>
        </Box>
        
        {currentFolder && (
          <Button 
            variant="text" 
            onClick={handleBackToRoot}
            sx={{ mb: 2 }}
          >
            ← Back to All Files
          </Button>
        )}
        
        <Paper
          elevation={0}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ p: 3 }}>
              {!currentFolder && folders.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Folders
                  </Typography>
                  <List>
                    {folders.map((folder) => (
                      <ListItem 
                        key={folder.id}
                        secondaryAction={
                          <Typography variant="body2" color="text.secondary">
                            {folder.date}
                          </Typography>
                        }
                        sx={{ 
                          cursor: 'pointer',
                          '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                        }}
                        onClick={() => handleFolderClick(folder)}
                      >
                        <ListItemIcon>
                          <FolderIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={folder.name} 
                        />
                      </ListItem>
                    ))}
                  </List>
                  <Divider sx={{ my: 2 }} />
                </>
              )}
              
              <Typography variant="h6" gutterBottom>
                Files
              </Typography>
              {files.length === 0 ? (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    No files found in this location
                  </Typography>
                </Box>
              ) : (
                <List>
                  {files.map((file) => (
                    <ListItem 
                      key={file.id}
                      secondaryAction={
                        <Box>
                          <IconButton 
                            aria-label="download"
                            onClick={() => handleDownloadFile(file.url, file.name)}
                          >
                            <DownloadIcon />
                          </IconButton>
                          <IconButton 
                            aria-label="delete" 
                            color="error"
                            onClick={() => handleDeleteFile(file.id, file.path)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      }
                    >
                      <ListItemIcon>
                        {getFileIcon(file.type)}
                      </ListItemIcon>
                      <ListItemText 
                        primary={file.name} 
                        secondary={
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <Chip 
                              label={file.size} 
                              size="small" 
                              variant="outlined" 
                            />
                            <Typography variant="body2" color="text.secondary">
                              {file.date}
                            </Typography>
                          </Box>
                        } 
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          )}
        </Paper>
      </Container>
      
      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={handleCloseUploadDialog}>
        <DialogTitle>Upload Medical Record</DialogTitle>
        <DialogContent>
          <Box sx={{ minWidth: 400, mt: 2 }}>
            {!selectedFile ? (
              <Box 
                sx={{ 
                  border: '2px dashed', 
                  borderColor: 'divider',
                  borderRadius: 2,
                  p: 3,
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' }
                }}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                <UploadIcon sx={{ fontSize: 40, color: 'primary.main', mb: 2 }} />
                <Typography variant="body1" gutterBottom>
                  Click to select a file or drag and drop
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Supports PDF, JPG, PNG, and other document formats
                </Typography>
                <input
                  id="file-input"
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  style={{ display: 'none' }}
                  onChange={handleFileSelect}
                />
              </Box>
            ) : (
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {getFileIcon(selectedFile.name.split('.').pop()?.toLowerCase() || '')}
                  <Box sx={{ ml: 2 }}>
                    <Typography variant="body1" fontWeight="medium">
                      {selectedFile.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formatFileSize(selectedFile.size)}
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    File Category:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {['medical_records', 'lab_results', 'prescriptions', 'imaging'].map((category) => (
                      <Chip
                        key={category}
                        label={category.replace('_', ' ')}
                        onClick={() => setFileCategory(category as FileCategory)}
                        color={fileCategory === category ? 'primary' : 'default'}
                        variant={fileCategory === category ? 'filled' : 'outlined'}
                        sx={{ textTransform: 'capitalize' }}
                      />
                    ))}
                  </Box>
                </Box>
                
                {uploadState.isUploading && (
                  <Box sx={{ width: '100%', mt: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress variant="determinate" value={uploadState.progress} />
                      </Box>
                      <Box sx={{ minWidth: 35 }}>
                        <Typography variant="body2" color="text.secondary">{`${Math.round(
                          uploadState.progress,
                        )}%`}</Typography>
                      </Box>
                    </Box>
                  </Box>
                )}
                
                {uploadState.error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {uploadState.error}
                  </Alert>
                )}
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUploadDialog}>Cancel</Button>
          <Button 
            onClick={handleUploadFile} 
            variant="contained" 
            disabled={!selectedFile || uploadState.isUploading}
          >
            {uploadState.isUploading ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* New Folder Dialog */}
      <Dialog open={newFolderDialogOpen} onClose={handleCloseNewFolderDialog}>
        <DialogTitle>Create New Folder</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Folder Name"
            type="text"
            fullWidth
            variant="outlined"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseNewFolderDialog}>Cancel</Button>
          <Button 
            onClick={handleCreateFolder}
            variant="contained"
            disabled={!newFolderName.trim()}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default FileManagementPage;







