import React, { useState } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import MedicalRecordAnalytics from '../../components/medical/MedicalRecordAnalytics';

// Icons
import DescriptionIcon from '@mui/icons-material/Description';
import BarChartIcon from '@mui/icons-material/BarChart';
import PieChartIcon from '@mui/icons-material/PieChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import DownloadIcon from '@mui/icons-material/Download';
import PrintIcon from '@mui/icons-material/Print';
import ShareIcon from '@mui/icons-material/Share';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';

const AdminReports = () => {
  const navigate = useNavigate();
  const [reportType, setReportType] = useState('user');
  const [dateRange, setDateRange] = useState('month');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Mock data for available reports
  const availableReports = [
    { id: 1, title: 'User Registration Report', type: 'user', icon: <BarChartIcon /> },
    { id: 2, title: 'Appointment Analytics', type: 'appointment', icon: <PieChartIcon /> },
    { id: 3, title: 'Doctor Activity Report', type: 'doctor', icon: <TimelineIcon /> },
    { id: 4, title: 'System Usage Statistics', type: 'system', icon: <BarChartIcon /> },
    { id: 5, title: 'Health Metrics Overview', type: 'health', icon: <TimelineIcon /> },
    { id: 6, title: 'Student Engagement Report', type: 'student', icon: <PieChartIcon /> }
  ];

  // Mock data for medical record analytics
  const medicalAnalytics = {
    monthlyRecords: [
      { month: 'Jan', count: 12 },
      { month: 'Feb', count: 19 },
      { month: 'Mar', count: 15 },
      { month: 'Apr', count: 22 },
      { month: 'May', count: 32 },
      { month: 'Jun', count: 27 }
    ],
    diagnosisDistribution: [
      { name: 'Common Cold', value: 28 },
      { name: 'Anxiety', value: 22 },
      { name: 'Allergic Reaction', value: 17 },
      { name: 'Sprained Ankle', value: 12 },
      { name: 'Insomnia', value: 10 }
    ],
    doctorWorkload: [
      { name: 'Dr. Sarah Johnson', records: 45 },
      { name: 'Dr. Michael Chen', records: 38 },
      { name: 'Dr. Emily Wilson', records: 32 },
      { name: 'Dr. Robert Davis', records: 24 },
      { name: 'Dr. Lisa Patel', records: 17 }
    ]
  };

  // Filter reports based on selected type
  const filteredReports = reportType === 'all' 
    ? availableReports 
    : availableReports.filter(report => report.type === reportType);

  // Function to toggle analytics view
  const toggleAnalytics = () => {
    setShowAnalytics(!showAnalytics);
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Reports & Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate and view reports about system usage and performance
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Report Filters */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Report Filters
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ mb: 3 }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="report-type-label">Report Type</InputLabel>
                  <Select
                    labelId="report-type-label"
                    id="report-type"
                    value={reportType}
                    label="Report Type"
                    onChange={(e) => setReportType(e.target.value)}
                  >
                    <MenuItem value="all">All Reports</MenuItem>
                    <MenuItem value="user">User Reports</MenuItem>
                    <MenuItem value="appointment">Appointment Reports</MenuItem>
                    <MenuItem value="doctor">Doctor Reports</MenuItem>
                    <MenuItem value="student">Student Reports</MenuItem>
                    <MenuItem value="system">System Reports</MenuItem>
                    <MenuItem value="health">Health Reports</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="date-range-label">Date Range</InputLabel>
                  <Select
                    labelId="date-range-label"
                    id="date-range"
                    value={dateRange}
                    label="Date Range"
                    onChange={(e) => setDateRange(e.target.value)}
                  >
                    <MenuItem value="today">Today</MenuItem>
                    <MenuItem value="week">This Week</MenuItem>
                    <MenuItem value="month">This Month</MenuItem>
                    <MenuItem value="quarter">This Quarter</MenuItem>
                    <MenuItem value="year">This Year</MenuItem>
                    <MenuItem value="custom">Custom Range</MenuItem>
                  </Select>
                </FormControl>
                
                {dateRange === 'custom' && (
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      label="Start Date"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      fullWidth
                      sx={{ mb: 2 }}
                    />
                    <TextField
                      label="End Date"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      fullWidth
                    />
                  </Box>
                )}
              </Box>
              
              <Button 
                variant="contained" 
                color="primary"
                fullWidth
                sx={{ 
                  borderRadius: 2,
                  py: 1.5
                }}
              >
                Generate Report
              </Button>
            </Paper>
          </Grid>

          {/* Available Reports */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Available Reports
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <Grid container spacing={2}>
                {filteredReports.map((report) => (
                  <Grid item xs={12} sm={6} key={report.id}>
                    <Card 
                      sx={{ 
                        borderRadius: 3,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                      }}
                    >
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Box 
                            sx={{ 
                              p: 1.5, 
                              borderRadius: 2, 
                              bgcolor: 'primary.light', 
                              color: 'primary.main',
                              mr: 2
                            }}
                          >
                            {report.icon}
                          </Box>
                          <Typography variant="h6" fontWeight="medium">
                            {report.title}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          View detailed analytics and statistics about {report.type} activity in the system.
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 2, pt: 0 }}>
                        <Button 
                          size="small" 
                          startIcon={<DescriptionIcon />}
                          sx={{ mr: 1 }}
                        >
                          View
                        </Button>
                        <Button 
                          size="small" 
                          startIcon={<DownloadIcon />}
                        >
                          Export
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
              
              {filteredReports.length === 0 && (
                <Box sx={{ py: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    No reports available for the selected type
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Medical Records Analytics */}
          {reportType === 'health' && (
            <Box sx={{ mt: 4 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2" fontWeight="bold">
                  Medical Records Analytics
                </Typography>
                <Button 
                  variant="outlined" 
                  onClick={toggleAnalytics}
                  sx={{ borderRadius: 2 }}
                >
                  {showAnalytics ? 'Hide Analytics' : 'Show Analytics'}
                </Button>
              </Box>
              
              {showAnalytics && (
                <MedicalRecordAnalytics 
                  monthlyRecords={medicalAnalytics.monthlyRecords}
                  diagnosisDistribution={medicalAnalytics.diagnosisDistribution}
                  doctorWorkload={medicalAnalytics.doctorWorkload}
                />
              )}
            </Box>
          )}

          {/* Recent Reports */}
          <Grid item xs={12}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                mt: 3
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Recently Generated Reports
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <List>
                <ListItem 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                >
                  <ListItemIcon>
                    <DescriptionIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Monthly User Registration Report" 
                    secondary="Generated on May 10, 2023" 
                  />
                  <Button 
                    size="small" 
                    startIcon={<DownloadIcon />}
                    sx={{ mr: 1 }}
                  >
                    Download
                  </Button>
                  <Button 
                    size="small" 
                    startIcon={<PrintIcon />}
                  >
                    Print
                  </Button>
                </ListItem>
                
                <ListItem 
                  sx={{ 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                >
                  <ListItemIcon>
                    <DescriptionIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Doctor Activity Summary" 
                    secondary="Generated on May 8, 2023" 
                  />
                  <Button 
                    size="small" 
                    startIcon={<DownloadIcon />}
                    sx={{ mr: 1 }}
                  >
                    Download
                  </Button>
                  <Button 
                    size="small" 
                    startIcon={<PrintIcon />}
                  >
                    Print
                  </Button>
                </ListItem>
                
                <ListItem 
                  sx={{ 
                    borderRadius: 2,
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                >
                  <ListItemIcon>
                    <DescriptionIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Appointment Analytics Q1 2023" 
                    secondary="Generated on May 5, 2023" 
                  />
                  <Button 
                    size="small" 
                    startIcon={<DownloadIcon />}
                    sx={{ mr: 1 }}
                  >
                    Download
                  </Button>
                  <Button 
                    size="small" 
                    startIcon={<PrintIcon />}
                  >
                    Print
                  </Button>
                </ListItem>
              </List>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default AdminReports;



