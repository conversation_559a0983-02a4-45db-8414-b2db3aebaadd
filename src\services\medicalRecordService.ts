import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';

export interface MedicalRecord {
  id?: string;
  studentId: string;
  studentName: string;
  doctorId: string;
  doctorName: string;
  date: string;
  diagnosis: string;
  symptoms: string[];
  treatment: string;
  medications: string[];
  notes?: string;
  followUpRequired: boolean;
  followUpDate?: string;
  status: 'active' | 'resolved' | 'ongoing';
  createdAt: Date;
  updatedAt: Date;
}

export interface DiagnosisCount {
  name: string;
  count: number;
}

/**
 * Create a new medical record
 */
export const createMedicalRecord = async (recordData: Omit<MedicalRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, 'medicalRecords'), {
      ...recordData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    console.log('✅ Medical record created with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('❌ Error creating medical record:', error);
    throw error;
  }
};

/**
 * Get all medical records
 */
export const getAllMedicalRecords = async (): Promise<MedicalRecord[]> => {
  try {
    const querySnapshot = await getDocs(
      query(collection(db, 'medicalRecords'), orderBy('createdAt', 'desc'))
    );
    
    const records: MedicalRecord[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      records.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as MedicalRecord);
    });
    
    console.log(`📋 Retrieved ${records.length} medical records`);
    return records;
  } catch (error) {
    console.error('❌ Error fetching medical records:', error);
    return [];
  }
};

/**
 * Get recent medical records (last 10)
 */
export const getRecentMedicalRecords = async (): Promise<MedicalRecord[]> => {
  try {
    const querySnapshot = await getDocs(
      query(
        collection(db, 'medicalRecords'),
        orderBy('createdAt', 'desc'),
        limit(10)
      )
    );
    
    const records: MedicalRecord[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      records.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as MedicalRecord);
    });
    
    console.log(`📋 Retrieved ${records.length} recent medical records`);
    return records;
  } catch (error) {
    console.error('❌ Error fetching recent medical records:', error);
    return [];
  }
};

/**
 * Get common diagnoses with counts
 */
export const getCommonDiagnoses = async (): Promise<DiagnosisCount[]> => {
  try {
    const records = await getAllMedicalRecords();
    
    // Count diagnoses
    const diagnosisMap = new Map<string, number>();
    records.forEach(record => {
      const diagnosis = record.diagnosis.trim();
      diagnosisMap.set(diagnosis, (diagnosisMap.get(diagnosis) || 0) + 1);
    });
    
    // Convert to array and sort by count
    const diagnoses: DiagnosisCount[] = Array.from(diagnosisMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10
    
    console.log(`📊 Retrieved ${diagnoses.length} common diagnoses`);
    return diagnoses;
  } catch (error) {
    console.error('❌ Error getting common diagnoses:', error);
    return [];
  }
};

/**
 * Get medical record statistics
 */
export const getMedicalRecordStats = async () => {
  try {
    const allRecords = await getAllMedicalRecords();
    
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const thisMonthRecords = allRecords.filter(record => 
      new Date(record.createdAt) >= thisMonth
    );

    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    lastMonth.setDate(1);
    const lastMonthEnd = new Date();
    lastMonthEnd.setDate(0);
    
    const lastMonthRecords = allRecords.filter(record => {
      const recordDate = new Date(record.createdAt);
      return recordDate >= lastMonth && recordDate <= lastMonthEnd;
    });

    const upcomingFollowUps = allRecords.filter(record => 
      record.followUpRequired && 
      record.followUpDate && 
      new Date(record.followUpDate) > new Date()
    ).length;

    const activeStudents = new Set(allRecords.map(record => record.studentId)).size;

    const monthlyChange = lastMonthRecords.length > 0 
      ? Math.round(((thisMonthRecords.length - lastMonthRecords.length) / lastMonthRecords.length) * 100)
      : 0;

    return {
      totalRecords: allRecords.length,
      recordsThisMonth: thisMonthRecords.length,
      upcomingFollowUps,
      activeStudents,
      monthlyChange,
      trend: monthlyChange >= 0 ? 'up' : 'down'
    };
  } catch (error) {
    console.error('❌ Error getting medical record stats:', error);
    return {
      totalRecords: 0,
      recordsThisMonth: 0,
      upcomingFollowUps: 0,
      activeStudents: 0,
      monthlyChange: 0,
      trend: 'up' as const
    };
  }
};

/**
 * Create sample medical records for testing
 */
export const createSampleMedicalRecords = async (): Promise<void> => {
  const sampleRecords = [
    {
      studentId: 'student1',
      studentName: 'John Smith',
      doctorId: 'doctor1',
      doctorName: 'Dr. Sarah Johnson',
      date: new Date().toISOString().split('T')[0],
      diagnosis: 'Common Cold',
      symptoms: ['Runny nose', 'Cough', 'Sore throat'],
      treatment: 'Rest and fluids',
      medications: ['Acetaminophen', 'Cough syrup'],
      notes: 'Patient should rest for 3-5 days',
      followUpRequired: false,
      status: 'resolved' as const
    },
    {
      studentId: 'student2',
      studentName: 'Emma Davis',
      doctorId: 'doctor2',
      doctorName: 'Dr. Michael Chen',
      date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
      diagnosis: 'Anxiety',
      symptoms: ['Nervousness', 'Rapid heartbeat', 'Sweating'],
      treatment: 'Cognitive behavioral therapy',
      medications: ['Sertraline 50mg'],
      notes: 'Patient responding well to treatment',
      followUpRequired: true,
      followUpDate: new Date(Date.now() + 7 * 86400000).toISOString().split('T')[0], // Next week
      status: 'ongoing' as const
    },
    {
      studentId: 'student3',
      studentName: 'Alex Rodriguez',
      doctorId: 'doctor1',
      doctorName: 'Dr. Sarah Johnson',
      date: new Date(Date.now() - 2 * 86400000).toISOString().split('T')[0], // 2 days ago
      diagnosis: 'Allergic Reaction',
      symptoms: ['Skin rash', 'Itching', 'Swelling'],
      treatment: 'Antihistamines and topical cream',
      medications: ['Benadryl', 'Hydrocortisone cream'],
      notes: 'Avoid known allergens',
      followUpRequired: true,
      followUpDate: new Date(Date.now() + 3 * 86400000).toISOString().split('T')[0], // In 3 days
      status: 'active' as const
    },
    {
      studentId: 'student4',
      studentName: 'Lisa Wang',
      doctorId: 'doctor3',
      doctorName: 'Dr. Emily Wilson',
      date: new Date(Date.now() - 3 * 86400000).toISOString().split('T')[0], // 3 days ago
      diagnosis: 'Sprained Ankle',
      symptoms: ['Pain', 'Swelling', 'Limited mobility'],
      treatment: 'RICE protocol (Rest, Ice, Compression, Elevation)',
      medications: ['Ibuprofen'],
      notes: 'Patient should avoid weight-bearing activities',
      followUpRequired: true,
      followUpDate: new Date(Date.now() + 5 * 86400000).toISOString().split('T')[0], // In 5 days
      status: 'active' as const
    },
    {
      studentId: 'student5',
      studentName: 'Mike Johnson',
      doctorId: 'doctor2',
      doctorName: 'Dr. Michael Chen',
      date: new Date(Date.now() - 5 * 86400000).toISOString().split('T')[0], // 5 days ago
      diagnosis: 'Insomnia',
      symptoms: ['Difficulty falling asleep', 'Frequent waking', 'Fatigue'],
      treatment: 'Sleep hygiene education',
      medications: ['Melatonin 3mg'],
      notes: 'Recommend establishing regular sleep schedule',
      followUpRequired: true,
      followUpDate: new Date(Date.now() + 14 * 86400000).toISOString().split('T')[0], // In 2 weeks
      status: 'ongoing' as const
    }
  ];

  try {
    for (const record of sampleRecords) {
      await createMedicalRecord(record);
    }
    console.log('✅ Sample medical records created');
  } catch (error) {
    console.error('❌ Error creating sample medical records:', error);
  }
};
