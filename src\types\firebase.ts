
import type { User } from 'firebase/auth';

// User types - use Firebase's built-in User type
export type FirebaseUser = User;

// User roles as both type and value
export const UserRoles = {
  STUDENT: 'student',
  DOCTOR: 'doctor',
  ADMIN: 'admin'
} as const;

export type UserRole = typeof UserRoles[keyof typeof UserRoles];

// User profile data
export interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  role: UserRole;
  phoneNumber?: string;
  department?: string;
  specialty?: string; // For doctors
  studentId?: string; // For students
  createdAt: Date;
  updatedAt: Date;
}


