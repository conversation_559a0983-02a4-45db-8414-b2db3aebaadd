import React, { useState } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Avatar,
  Chip,
  TextField,
  InputAdornment,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Card,
  CardContent,
  CardActions,
  ToggleButtonGroup,
  ToggleButton
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';

// Icons
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import StarIcon from '@mui/icons-material/Star';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';

const AdminDoctors = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSpecialty, setFilterSpecialty] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'table'
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  
  // Mock data for doctors
  const doctors = [
    { 
      id: 1, 
      name: 'Dr. Sarah Johnson', 
      email: '<EMAIL>', 
      specialty: 'General Medicine', 
      status: 'active', 
      joinDate: '2023-01-15',
      appointments: 145,
      rating: 4.8,
      avatar: 'SJ'
    },
    { 
      id: 2, 
      name: 'Dr. Michael Chen', 
      email: '<EMAIL>', 
      specialty: 'Psychiatry', 
      status: 'active', 
      joinDate: '2023-02-10',
      appointments: 98,
      rating: 4.6,
      avatar: 'MC'
    },
    { 
      id: 3, 
      name: 'Dr. Emily White', 
      email: '<EMAIL>', 
      specialty: 'Dermatology', 
      status: 'active', 
      joinDate: '2023-03-05',
      appointments: 112,
      rating: 4.9,
      avatar: 'EW'
    },
    { 
      id: 4, 
      name: 'Dr. James Wilson', 
      email: '<EMAIL>', 
      specialty: 'Neurology', 
      status: 'inactive', 
      joinDate: '2023-01-20',
      appointments: 87,
      rating: 4.7,
      avatar: 'JW'
    },
    { 
      id: 5, 
      name: 'Dr. Lisa Rodriguez', 
      email: '<EMAIL>', 
      specialty: 'Cardiology', 
      status: 'active', 
      joinDate: '2023-02-25',
      appointments: 132,
      rating: 4.5,
      avatar: 'LR'
    },
    { 
      id: 6, 
      name: 'Dr. David Kim', 
      email: '<EMAIL>', 
      specialty: 'Orthopedics', 
      status: 'active', 
      joinDate: '2023-03-15',
      appointments: 104,
      rating: 4.8,
      avatar: 'DK'
    },
    { 
      id: 7, 
      name: 'Dr. Maria Garcia', 
      email: '<EMAIL>', 
      specialty: 'Ophthalmology', 
      status: 'active', 
      joinDate: '2023-02-05',
      appointments: 95,
      rating: 4.7,
      avatar: 'MG'
    },
    { 
      id: 8, 
      name: 'Dr. Robert Taylor', 
      email: '<EMAIL>', 
      specialty: 'General Medicine', 
      status: 'inactive', 
      joinDate: '2023-01-10',
      appointments: 76,
      rating: 4.4,
      avatar: 'RT'
    },
    { 
      id: 9, 
      name: 'Dr. Jennifer Lee', 
      email: '<EMAIL>', 
      specialty: 'Psychiatry', 
      status: 'active', 
      joinDate: '2023-03-20',
      appointments: 89,
      rating: 4.9,
      avatar: 'JL'
    },
    { 
      id: 10, 
      name: 'Dr. Thomas Brown', 
      email: '<EMAIL>', 
      specialty: 'Dermatology', 
      status: 'active', 
      joinDate: '2023-02-15',
      appointments: 110,
      rating: 4.6,
      avatar: 'TB'
    }
  ];

  // Get unique specialties for filter
  const specialties = [...new Set(doctors.map(doctor => doctor.specialty))];

  // Filter doctors based on search term and filters
  const filteredDoctors = doctors.filter(doctor => {
    const matchesSearch = doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          doctor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSpecialty = filterSpecialty === 'all' || doctor.specialty === filterSpecialty;
    const matchesStatus = filterStatus === 'all' || doctor.status === filterStatus;
    
    return matchesSearch && matchesSpecialty && matchesStatus;
  });

  // Pagination handlers
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // View mode handler
  const handleViewModeChange = (event, newMode) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  };

  // Menu handlers
  const handleOpenMenu = (event, doctor) => {
    setAnchorEl(event.currentTarget);
    setSelectedDoctor(doctor);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  // Delete dialog handlers
  const handleOpenDeleteDialog = () => {
    setOpenDeleteDialog(true);
    handleCloseMenu();
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedDoctor(null);
  };

  const handleDeleteDoctor = () => {
    // Here you would implement the actual deletion logic
    console.log(`Deleting doctor with ID: ${selectedDoctor.id}`);
    handleCloseDeleteDialog();
  };

  // Render grid view
  const renderGridView = () => {
    return (
      <Grid container spacing={3}>
        {filteredDoctors
          .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
          .map((doctor) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={doctor.id}>
              <Card 
                sx={{ 
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                    <Avatar 
                      sx={{ 
                        width: 80, 
                        height: 80, 
                        mb: 2,
                        bgcolor: 'primary.main',
                        fontSize: '1.5rem'
                      }}
                    >
                      {doctor.avatar}
                    </Avatar>
                    <Typography variant="h6" fontWeight="bold" align="center">
                      {doctor.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" align="center" gutterBottom>
                      {doctor.email}
                    </Typography>
                    <Chip 
                      label={doctor.specialty}
                      color="primary"
                      size="small"
                      sx={{ mb: 1 }}
                    />
                    <Chip 
                      label={doctor.status.charAt(0).toUpperCase() + doctor.status.slice(1)}
                      color={doctor.status === 'active' ? 'success' : 'error'}
                      variant="outlined"
                      size="small"
                    />
                  </Box>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Appointments:
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <EventAvailableIcon fontSize="small" sx={{ mr: 0.5, color: 'primary.main' }} />
                      <Typography variant="body2" fontWeight="medium">
                        {doctor.appointments}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Rating:
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <StarIcon fontSize="small" sx={{ mr: 0.5, color: 'warning.main' }} />
                      <Typography variant="body2" fontWeight="medium">
                        {doctor.rating}/5.0
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
                
                <CardActions sx={{ p: 2, pt: 0 }}>
                  <Button 
                    size="small" 
                    startIcon={<VisibilityIcon />}
                    onClick={() => navigate(`/admin/doctors/${doctor.id}`)}
                    sx={{ mr: 1 }}
                  >
                    View
                  </Button>
                  <Button 
                    size="small" 
                    startIcon={<EditIcon />}
                    onClick={() => navigate(`/admin/doctors/${doctor.id}/edit`)}
                  >
                    Edit
                  </Button>
                  <IconButton 
                    size="small" 
                    color="error"
                    onClick={(e) => handleOpenMenu(e, doctor)}
                    sx={{ ml: 'auto' }}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
      </Grid>
    );
  };

  // Render table view
  const renderTableView = () => {
    return (
      <TableContainer>
        <Table sx={{ minWidth: 650 }} aria-label="doctors table">
          <TableHead>
            <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Specialty</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Join Date</TableCell>
              <TableCell>Appointments</TableCell>
              <TableCell>Rating</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredDoctors
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((doctor) => (
                <TableRow
                  key={doctor.id}
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                >
                  <TableCell component="th" scope="row">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar 
                        sx={{ 
                          mr: 2, 
                          bgcolor: 'primary.main'
                        }}
                      >
                        {doctor.avatar}
                      </Avatar>
                      <Typography variant="body2" fontWeight="medium">
                        {doctor.name}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{doctor.email}</TableCell>
                  <TableCell>
                    <Chip 
                      label={doctor.specialty}
                      color="primary"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={doctor.status.charAt(0).toUpperCase() + doctor.status.slice(1)}
                      color={doctor.status === 'active' ? 'success' : 'error'}
                      variant="outlined"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{doctor.joinDate}</TableCell>
                  <TableCell>{doctor.appointments}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <StarIcon fontSize="small" sx={{ mr: 0.5, color: 'warning.main' }} />
                      <Typography variant="body2">
                        {doctor.rating}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <IconButton 
                      size="small" 
                      onClick={() => navigate(`/admin/doctors/${doctor.id}`)}
                    >
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                    <IconButton 
                      size="small" 
                      onClick={() => navigate(`/admin/doctors/${doctor.id}/edit`)}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton 
                      size="small" 
                      color="error"
                      onClick={(e) => handleOpenMenu(e, doctor)}
                    >
                      <MoreVertIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            {filteredDoctors.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    No doctors found matching your criteria
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Doctor Management
          </Typography>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/admin/doctors/new')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            Add New Doctor
          </Button>
        </Box>

        {/* Filters and Search */}
        <Paper 
          sx={{ 
            p: 3, 
            mb: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center', mb: 2 }}>
            <TextField
              placeholder="Search doctors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ flexGrow: 1, minWidth: '200px' }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              size="small"
            />
            
            <FormControl sx={{ minWidth: '180px' }} size="small">
              <InputLabel id="specialty-filter-label">Specialty</InputLabel>
              <Select
                labelId="specialty-filter-label"
                id="specialty-filter"
                value={filterSpecialty}
                label="Specialty"
                onChange={(e) => setFilterSpecialty(e.target.value)}
              >
                <MenuItem value="all">All Specialties</MenuItem>
                {specialties.map((specialty) => (
                  <MenuItem key={specialty} value={specialty}>{specialty}</MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <FormControl sx={{ minWidth: '150px' }} size="small">
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                id="status-filter"
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              {filteredDoctors.length} doctors found
            </Typography>
            
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              size="small"
            >
              <ToggleButton value="grid" aria-label="grid view">
                <GridViewIcon fontSize="small" />
              </ToggleButton>
              <ToggleButton value="table" aria-label="table view">
                <ViewListIcon fontSize="small" />
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </Paper>

        {/* Doctors List */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            p: viewMode === 'grid' ? 3 : 0
          }}
        >
          {viewMode === 'grid' ? renderGridView() : renderTableView()}
          
          <TablePagination
            rowsPerPageOptions={[8, 16, 24]}
            component="div"
            count={filteredDoctors.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{ borderTop: '1px solid rgba(0,0,0,0.1)' }}
          />
        </Paper>
      </Container>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        PaperProps={{
          elevation: 3,
          sx: { borderRadius: 2, minWidth: 150 }
        }}
      >
        <MenuItem onClick={() => {
          navigate(`/admin/doctors/${selectedDoctor?.id}`);
          handleCloseMenu();
        }}>
          <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => {
          navigate(`/admin/doctors/${selectedDoctor?.id}/edit`);
          handleCloseMenu();
        }}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit Doctor
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleOpenDeleteDialog} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete Doctor
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: { borderRadius: 3 }
        }}
      >
        <DialogTitle id="alert-dialog-title">
          {"Confirm Doctor Deletion"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to delete {selectedDoctor?.name}? This action cannot be undone and will remove all associated data.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button 
            onClick={handleCloseDeleteDialog} 
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteDoctor} 
            color="error" 
            variant="contained"
            sx={{ borderRadius: 2 }}
            autoFocus
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default AdminDoctors;
